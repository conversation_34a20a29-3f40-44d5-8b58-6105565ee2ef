import { HttpClient } from '@angular/common/http';
import { Component, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { DxDataGridComponent } from 'devextreme-angular/ui/data-grid';
import { ToastrService } from 'ngx-toastr';
import { combineLatest, Subscription } from 'rxjs';
import { UserProfile } from '../../../profile/models/user-profile';
import { DataGridService, UsersService } from '../../../shared/services';
import { CredoSoftService } from '../../services';

@Component({
    selector: 'app-aimaas-overview-dashboard',
    templateUrl: './aimaas-overview-dashboard.component.html',
    styleUrls: ['./aimaas-overview-dashboard.component.scss']
})
export class AimaasOverviewDashboardComponent implements OnInit {
    costCentreOptions: any[] = [];
    isLoading: boolean = true;
    clientcostcentre: any;
    tableData: any;
    assetsKPI: any;
    _inspections: any;
    anamolydata: any;
    currentdate = new Date();
    isincludeoutofservicechecked: any;
    complianceScore = 0;
    gaugeRanges = this.getGaugeRanges(this.complianceScore);
    inspectionChartData: any;
    chartOptions: any;
    gaugeOptions: {
        value: number;
        subvalueIndicator: { type: string };
        rangeContainer: {
            ranges: { startValue: number; endValue: number; color: string }[];
        };
    };
    locationData: any;
    overdueMap: any;
    pageNumber: number;
    recordsPerPage: number = 15;
    @ViewChild(DxDataGridComponent) dataGrid: DxDataGridComponent;
    selectedDistricts: any;
    selectedLocationIds: any;
    recommendationChartOptions: {
        location: any;
        dataSource: any;
        series: { valueField: string; name: string }[];
        argumentField: string;
    }[];
    crumbs: { label: any; route: string }[];
    availableSites: any;
    districtvalues: any[] = [];
    selectedClient: any[];
    costCentreToDistrictMap = new Map<string, any[]>(); // Maps cost centre to districts
    districtToCostCentreMap = new Map<string, any[]>(); // Maps district to cost centres
    filteredClientValues: any[];
    filteredDistrictOptions: any[];
    filteredLocations: any[];
    locationOptions: { id: string; name: string }[];
    currentUser: UserProfile;
    private routerSubscription: Subscription;
    totalAssets: any;
    currentInspections: any = [];
    overdueInspections: any = [];
    noInspections: any = [];
    inspectionsOverdew: any = [];
    dueThisYearInspections: any = [];
    dueNextYearInspections: any = [];
    openMajorsP6: any = [];
    openMinorsP5: any = [];
    openMonitors4: any = [];
    filterValue: Array<any>;
    roles: any[];
    isDemoUser: boolean = false;
    clientLocationData: any = [];
    private isApplyingFilters = false;
    onlyClientUser: boolean = false;

    constructor(
        private readonly _credoSoft: CredoSoftService,
        private http: HttpClient,
        private readonly _router: Router,
        private route: ActivatedRoute,
        private readonly _grid: DataGridService,
        private readonly _users: UsersService,
        private readonly _toasts: ToastrService
    ) { }

    ngOnInit(): void {
        this.isLoading = true;
        this.tableData = [];
        this.pageNumber = 0;
        this.recordsPerPage = 15;

        this.loadStoredFilters();

        combineLatest([
            this._credoSoft.assets$,
            this._credoSoft.inspections$,
            this._credoSoft.anomalies$,
            this._credoSoft.clientLocationData$,
            this._users.currentProfile$
        ]).subscribe(
            ([
                assets,
                inspections,
                anomalies,
                clientLocationData,
                currentUser
            ]) => {
                this.anamolydata = anomalies;
                this._inspections = inspections;
                this.assetsKPI = assets;
                this.clientLocationData = clientLocationData;
                this.clientcostcentre = clientLocationData;
                this.currentUser = currentUser;
                const roles = currentUser.roles.map((role) =>
                    role.toLowerCase()
                );
                this.roles = roles;
                this.anamolydata = anomalies;
                this._inspections = inspections;
                this.assetsKPI = assets;
                if (!clientLocationData || clientLocationData.length === 0) {
                    console.warn('No client location data available');
                    this._toasts.error(
                        'No client data for selected district',
                        'Validation Error'
                    );
                    this.isLoading = false;
                    return;
                }
                this.onlyClientUser = !roles?.includes('aimaas:admin') && !roles?.includes('app:admin') && roles?.includes('aimaas:client') && !roles?.includes('aimaas:demo');
                if (roles?.includes('aimaas:demo')) {
                    this.clientcostcentre = clientLocationData?.filter(
                        (site) =>
                            site?.locationid == Number('635140707384299520')
                    );
                    this.selectedClient = [this.clientcostcentre[0]?.clientid];
                    this.selectedDistricts = [
                        this.clientcostcentre[0]?.costcenterid
                    ];
                } else if (
                    roles?.includes('aimaas:view') ||
                    roles?.includes('aimaas:edit')
                ) {
                    this.clientcostcentre = clientLocationData.filter((site) =>
                        currentUser.assetManagementSiteIds.includes(
                            site?.locationid
                        )
                    );
                } else this.clientcostcentre = clientLocationData;
                this.processLocations(this.clientcostcentre);

                if (roles) {
                    if (roles.includes('aimaas:demo')) {
                        this.availableSites = this.availableSites?.filter(
                            (site) =>
                                site.locationid == Number('635140707384299520')
                        );
                    } else if (
                        !roles.includes('app:admin') &&
                        !roles.includes('aimaas:admin') &&
                        !roles.includes('aimaas:all') &&
                        !roles.includes('aimaas:district') &&
                        !roles.includes('aimaas:client') &&
                        currentUser.assetManagementSiteIds
                    ) {
                        this.availableSites = this.availableSites?.filter(
                            (site) =>
                                currentUser.assetManagementSiteIds.includes(
                                    site.locationid
                                )
                        );
                    }
                }

                // Initialize dropdowns and totals
                this.filteredDistrictOptions = [...this.costCentreOptions];
                this.filteredClientValues = [...this.districtvalues];
                this.filteredLocations = [];
                this.tableData = [];
                this.dynamicLocationData = [];
                this.chartOptions = [];
                this.gaugeOptions = {
                    value: 0,
                    subvalueIndicator: { type: 'textCloud' },
                    rangeContainer: { ranges: [] }
                };
                this.recommendationChartOptions = [];

                this.applyStoredFilters();
                this.ondisplayResults();

                setTimeout(() => {
                    this.refreshGridTotals(); // Refresh grid totals
                    this.syncDashboardWithTable(); // Sync dashboard with table data
                    this.isLoading = false; // <-- Only set to false after everything is ready
                }, 2000);

                this.updateBreadcrumbs();
                this.updateInspectionDataBasedOnLocation();
            }
        );
    }

    // eslint-disable-next-line @angular-eslint/use-lifecycle-interface
    ngOnDestroy(): void {
        if (this.routerSubscription) {
            this.routerSubscription.unsubscribe();
        }
    }
    restoreAssetsDefaultsClicked = async (e) => {
        await this._grid.resetGridState(this.dataGrid);
    };
    private loadStoredFilters() {
        const storedFilters = localStorage.getItem('filters');
        if (storedFilters) {
            const filters = JSON.parse(storedFilters);

            // Load stored filters into component properties
            this.selectedDistricts = filters.selectedDistricts || [];
            this.selectedClient = filters.selectedClients || [];
            this.selectedLocationIds = filters.selectedLocations || [];
            this.tableData = filters.tableData || [];
            this.chartOptions = filters.chartOptions || [];
            this.gaugeOptions = filters.gaugeOptions || {};
            this.recommendationChartOptions =
                filters.recommendationChartOptions || [];
            // Do NOT call this.applyStoredFilters() here!
        } else {
            // Reset everything if no filters are stored
            this.selectedDistricts = [];
            this.selectedClient = [];
            this.selectedLocationIds = [];
            this.tableData = [];
            this.dynamicLocationData = [];
            this.chartOptions = [];
            this.gaugeOptions = {
                value: 0,
                subvalueIndicator: { type: 'textCloud' },
                rangeContainer: { ranges: [] }
            };
            this.recommendationChartOptions = [];
        }
    }

    private saveFilters() {
        const filters = {
            selectedClients: this.selectedClient,
            selectedDistricts: this.selectedDistricts,
            selectedLocations: this.selectedLocationIds,
            tableData: this.tableData,
            chartOptions: this.chartOptions,
            gaugeOptions: this.gaugeOptions,
            recommendationChartOptions: this.recommendationChartOptions
        };

        localStorage.setItem('filters', JSON.stringify(filters));
    }

    /** Apply stored filters */
    private applyStoredFilters() {
        if (this.isApplyingFilters) return; // Prevent recursion
        this.isApplyingFilters = true;

        // Ensure arrays are always defined
        if (!Array.isArray(this.selectedDistricts)) this.selectedDistricts = [];
        if (!Array.isArray(this.selectedClient)) this.selectedClient = [];

        // If both are empty, assign the first available value and trigger change
        if (
            this.selectedDistricts.length === 0 &&
            this.selectedClient.length === 0
        ) {
            if (
                this.filteredDistrictOptions &&
                this.filteredDistrictOptions.length > 0
            ) {
                this.selectedDistricts = [this.filteredDistrictOptions[0].id];
                this.onDistrictChange(this.selectedDistricts);
                this.isApplyingFilters = false;
                return;
            } else if (
                this.filteredClientValues &&
                this.filteredClientValues.length > 0
            ) {
                this.selectedClient = [this.filteredClientValues[0].id];
                this.onClientChange(this.selectedClient);
                this.isApplyingFilters = false;
                return;
            } else {
                // No options available, clear loading
                this.isLoading = false;
                this.isApplyingFilters = false;
                return;
            }
        }

        // If either filter is set, always fetch locations
        this.fetchLocations();

        // Trigger grid and dashboard updates
        setTimeout(() => {
            this.refreshGridTotals();
            this.syncDashboardWithTable();
            this.isApplyingFilters = false;
        }, 1000);
    }

    filterDataBasedOnLocation(selectedLocationIds: string[]) {
        if (!selectedLocationIds || !Array.isArray(selectedLocationIds)) return;

        if (selectedLocationIds.length === 0) {
            this.tableData = [];
            return;
        }

        setTimeout(() => {
            this.selectedLocationIds = selectedLocationIds;
            this.saveFilters();

            const filteredAssets = this.assetsKPI.filter(
                (asset) =>
                    selectedLocationIds.includes(String(asset.locationid)) &&
                    asset.assetstatus !== 'Removed from Unit' &&
                    asset.assetstatus !== 'Out of Service'
            );

            const filteredAssetIds = new Set(filteredAssets.map((asset) => String(asset.id)));

            const filteredInspections = this._inspections?.filter(
                (inspection) => inspection.scheduletype != null
            ) || [];

            const inspectionMap = new Map<number, any[]>();
            filteredInspections.forEach((inspection) => {
                if (!inspectionMap.has(inspection.assetid)) inspectionMap.set(inspection.assetid, []);
                inspectionMap.get(inspection.assetid)!.push(inspection);
            });

            const filteredAnomalies = this.anamolydata.filter(
                (anomaly) =>
                    filteredAssetIds.has(String(anomaly.assetid)) &&
                    anomaly.resolutionstate === 'Recommended'
            );

            const anomalyMap = new Map<number, any[]>();
            filteredAnomalies.forEach((an) => {
                if (!anomalyMap.has(an.assetid)) anomalyMap.set(an.assetid, []);
                anomalyMap.get(an.assetid)!.push(an);
            });

            const currentDate = this.currentdate;
            const currentYear = currentDate.getFullYear();
            const nextYear = currentYear + 1;

            const aggregatedData: any = {};

            this.totalAssets = this.assetsKPI?.filter((asset) =>
                filteredInspections.some((ins) => ins.assetid === asset.id)
            );

            filteredAssets.forEach((asset) => {
                const locationId = asset.locationid;
                const locationName = asset.locationname;
                const inspections = inspectionMap.get(asset.id) || [];
                const anomalies = anomalyMap.get(asset.id) || [];

                if (!aggregatedData[locationId]) {
                    aggregatedData[locationId] = {
                        locationId: locationId,
                        location: locationName,
                        totalAssets: 0,
                        currentInspections: 0,
                        overdueInspections: 0,
                        noInspections: 0,
                        overdue: 0,
                        dueThisYear: 0,
                        dueNextYear: 0,
                        openMajors: 0,
                        openMinors: 0,
                        openMonitors: 0,
                    };
                }

                const group = aggregatedData[locationId];

                const hasFutureDue = inspections.some(
                    (i) => i.nextinspectiondue && new Date(i.nextinspectiondue) >= currentDate
                );
                const hasPastDue = inspections.some(
                    (i) => i.nextinspectiondue && new Date(i.nextinspectiondue) < currentDate
                );

                if (hasFutureDue && !hasPastDue) group.currentInspections += 1;

                if (
                    inspections.some(
                        (i) =>
                            i.nextinspectiondue &&
                            new Date(i.nextinspectiondue) < currentDate &&
                            i.schedulestatus === 'Active'
                    )
                ) {
                    group.overdueInspections += 1;
                }

                if (
                    inspections.every(
                        (i) =>
                            !i.nextinspectiondue ||
                            i.schedulestatus.trim().toLowerCase() !== 'active'
                    )
                ) {
                    group.noInspections += 1;
                }

                group.dueThisYear += inspections.filter(
                    (i) =>
                        i.scheduletype &&
                        i.schedulestatus.trim().toLowerCase() === 'active' &&
                        (!this.isincludeoutofservicechecked ||
                            (i.assetstatus !== 'Removed from Unit' &&
                                i.assetstatus !== 'Out of Service')) &&
                        i.isDueThisYear?.()
                ).length;

                group.overdue += inspections.filter(
                    (i) =>
                        i.nextinspectiondue &&
                        new Date(i.nextinspectiondue) < currentDate &&
                        i.schedulestatus.trim().toLowerCase() === 'active'
                ).length;

                group.dueNextYear += inspections.filter(
                    (i) =>
                        i.scheduletype &&
                        i.schedulestatus.trim().toLowerCase() === 'active' &&
                        (!this.isincludeoutofservicechecked ||
                            (i.assetstatus !== 'Removed from Unit' &&
                                i.assetstatus !== 'Out of Service')) &&
                        i.nextinspectiondue &&
                        new Date(i.nextinspectiondue).getFullYear() === nextYear
                ).length;

                group.totalAssets += 1;

                group.openMajors += anomalies.filter(
                    (a) =>
                        a.anomalypriority?.toString().trim().startsWith('6') &&
                        a.resolutionstate?.trim().toLowerCase() === 'recommended'
                ).length;

                group.openMinors += anomalies.filter(
                    (a) =>
                        a.anomalypriority?.toString().trim().startsWith('5') &&
                        a.resolutionstate?.trim().toLowerCase() === 'recommended'
                ).length;

                group.openMonitors += anomalies.filter(
                    (a) =>
                        a.anomalypriority?.toString().trim().startsWith('4') &&
                        a.resolutionstate?.trim().toLowerCase() === 'recommended'
                ).length;
            });

            // Delay setting tableData to prevent UI blocking
            setTimeout(() => {
                this.tableData = Object.values(aggregatedData);
            });

            // Save filters once after everything
            this.saveFilters();
        });
    }


    onPageChange(event: any) {
        if (event.fullName === 'paging.pageIndex') {
            this.pageNumber = event.value;
        }
        if (event.fullName === 'paging.pageSize') {
            this.recordsPerPage = event.value;
        }

        // Refresh the grid and synchronize dashboard data
        setTimeout(() => {
            event.component.refresh(true); // Force a full refresh
            this.syncDashboardWithTable(); // Synchronize dashboard with table data
        }, 300);
    }

    onRecordsPerPageChange(event: any) {
        if (event.fullName === 'paging.pageSize') {
            this.recordsPerPage = event.value;

            // Refresh the grid and synchronize dashboard data
            setTimeout(() => {
                event.component.refresh(true); // Force a full refresh
                this.syncDashboardWithTable(); // Synchronize dashboard with table data
            }, 100);
        }
    }

    onPageSummaryCalculation(e: any) {
        if (!e.component) return; // Prevent errors if the grid is not ready

        if (e.summaryProcess === 'start') {
            e.totalValue = 0; // Reset total for each summary field
        }

        if (e.summaryProcess === 'calculate') {
            // Get only the currently visible rows (for the current page)
            const visibleRows = e.component.getVisibleRows();

            // Find the field we are calculating the total for
            const field = e.name.replace('Summary', ''); // Extract field name from summary name

            // Sum values for the current field
            let pageTotal = 0;
            let totalAssets = 0;
            visibleRows.forEach((row) => {
                const value = parseFloat(row.data[field]) || 0;
                pageTotal += value;
                // Always get totalAssets for percentage calculation
                totalAssets += parseFloat(row.data['totalAssets']) || 0;
            });

            // Calculate percentage (if applicable)
            let percentage = '';
            if (
                [
                    'currentInspections',
                    'overdueInspections',
                    'noInspections'
                ].includes(field)
            ) {
                if (totalAssets > 0) {
                    const percentageValue = (pageTotal / totalAssets) * 100;
                    percentage = ` (${Math.round(percentageValue)}%)`; // Round to nearest whole number
                } else {
                    percentage = ' (0%)'; // Avoid division by zero
                }
            }

            // Set the calculated value with percentage
            e.totalValue = `${pageTotal}${percentage}`;
        }
    }

    updateDashboardContent(pageIndex: number) {
        const pageSize = this.recordsPerPage;
        const startIndex = pageIndex * pageSize;
        const endIndex = startIndex + pageSize;

        // Ensure tableData is valid before slicing
        if (this.tableData && this.tableData.length > 0) {
            this.dynamicLocationData = this.tableData.slice(
                startIndex,
                endIndex
            );
        } else {
            this.dynamicLocationData = []; // Clear the dashboard if no data is available
        }

        // Update dashboard charts and gauges
        if (this.dynamicLocationData.length > 0) {
            this.chartOptions = this.dynamicLocationData.map((data) => ({
                location: data.location,
                dataSource: data.inspectionChartData || [],
                series: [
                    { valueField: 'Overdue', name: 'Overdue' },
                    { valueField: 'DueThisYear', name: 'Due This Year' },
                    { valueField: 'DueNextYear', name: 'Due Next Year' }
                ],
                argumentField: 'category'
            }));

            this.gaugeOptions = {
                value: this.dynamicLocationData[0]?.complianceScore || 0,
                subvalueIndicator: { type: 'textCloud' },
                rangeContainer: {
                    ranges: this.getGaugeRanges(
                        this.dynamicLocationData[0]?.complianceScore || 0
                    )
                }
            };
        }
    }

    syncDashboardWithTable() {
        // Get visible rows from the data grid
        const visibleRows = this.dataGrid?.instance?.getVisibleRows();

        if (!visibleRows || visibleRows.length === 0) {
            // If the table is empty, clear the dashboard
            this.dynamicLocationData = [];
            this.chartOptions = [];
            this.gaugeOptions = {
                value: 0,
                subvalueIndicator: { type: 'textCloud' },
                rangeContainer: { ranges: [] }
            };
            this.recommendationChartOptions = [];
            return;
        }

        // Extract only the data objects from visible rows
        const visibleData = visibleRows.map((row) => row.data);

        // Update dashboard values based on visible rows
        this.dynamicLocationData = visibleData.map((row) => ({
            location: row.location,
            complianceScore: this.calculateComplianceScore(row.locationId),
            inspectionChartData: this.getInspectionDataByLocation(
                row.locationId
            ),
            recommendationChartData: this.getRecommendationDataByLocation(
                row.locationId
            )
        }));

        // Update dashboard charts and gauges
        this.chartOptions = this.dynamicLocationData.map((data) => ({
            location: data.location,
            dataSource: data.inspectionChartData || [],
            series: [
                { valueField: 'Overdue', name: 'Overdue' },
                { valueField: 'DueThisYear', name: 'Due This Year' },
                { valueField: 'DueNextYear', name: 'Due Next Year' }
            ],
            argumentField: 'category'
        }));

        this.gaugeOptions = {
            value: this.dynamicLocationData[0]?.complianceScore || 0,
            subvalueIndicator: { type: 'textCloud' },
            rangeContainer: {
                ranges: this.getGaugeRanges(
                    this.dynamicLocationData[0]?.complianceScore || 0
                )
            }
        };

        this.recommendationChartOptions = this.dynamicLocationData.map(
            (data) => ({
                location: data.location,
                dataSource: data.recommendationChartData || [],
                series: [{ valueField: 'value', name: 'Value' }],
                argumentField: 'category'
            })
        );

        // Ensure totals are recalculated for the filtered data
        this.refreshGridTotals();
    }

    formatCurrentInspections(rowData: any): string {
        if (rowData) {
            return `${rowData.currentInspections} (${Math.round(
                (rowData.currentInspections / rowData.totalAssets) * 100
            )}%)`;
        }
    }
    formatOverdueInspections(rowData: any): string {
        if (rowData) {
            return `${rowData.overdueInspections} (${Math.round(
                (rowData.overdueInspections / rowData.totalAssets) * 100
            )}%)`;
        }
    }
    formatNoInspections(rowData: any): string {
        if (rowData) {
            return `${rowData.noInspections} (${Math.round(
                (rowData.noInspections / rowData.totalAssets) * 100
            )}%)`;
        }
    }

    processLocations(data: any[]) {
        const costCentreMap = new Map<string, string>();
        const clientMap = new Map<string, string>();
        const CostCentresToDistrictMap = new Map<string, Set<any>>();
        const DistrictsToCostCentreMap = new Map<string, Set<any>>();
        const locationMap = new Map<string, string>(); // Add this for locations

        if (this.isDemoUser) {
            if (!data || data.length === 0) {
                console.warn(
                    'No data passed to processLocations for demo user'
                );
                return;
            }

            const locationId = data[0].locationid;
            data = this.clientcostcentre.filter(
                (site) => site.locationid === locationId
            );

            if (!data || data.length === 0) {
                console.warn(
                    'Filtered clientcostcentre has no matching entries'
                );
                return;
            }

            data.forEach((item) => {
                if (item?.costcenterid && item?.costcentername) {
                    costCentreMap.set(
                        item.costcenterid,
                        item.costcentername.trim()
                    );
                }
                if (item?.clientid && item?.clientname) {
                    clientMap.set(item.clientid, item.clientname.trim());
                }
                if (item?.locationid && item?.locationname) {
                    locationMap.set(item.locationid, item.locationname.trim());
                }

                if (item?.costcenterid && item?.clientid && item?.clientname) {
                    if (!CostCentresToDistrictMap.has(item.costcenterid)) {
                        CostCentresToDistrictMap.set(
                            item.costcenterid,
                            new Set()
                        );
                    }
                    CostCentresToDistrictMap.get(item.costcenterid)?.add({
                        id: item.clientid,
                        name: item.clientname.trim()
                    });
                }

                if (
                    item?.clientid &&
                    item?.costcenterid &&
                    item?.costcentername
                ) {
                    if (!DistrictsToCostCentreMap.has(item.clientid)) {
                        DistrictsToCostCentreMap.set(item.clientid, new Set());
                    }
                    DistrictsToCostCentreMap.get(item.clientid)?.add({
                        id: item.costcenterid,
                        name: item.costcentername.trim()
                    });
                }
            });

            // Same mapping logic
            this.costCentreOptions = Array.from(costCentreMap.entries())
                .map(([id, name]) => ({ id, name }))
                .sort((a, b) => a.name.localeCompare(b.name));

            this.districtvalues = Array.from(clientMap.entries())
                .map(([id, name]) => ({ id, name }))
                .sort((a, b) => a.name.localeCompare(b.name));

            this.locationOptions = Array.from(locationMap.entries())
                .map(([id, name]) => ({ id, name }))
                .sort((a, b) => a.name.localeCompare(b.name));

            CostCentresToDistrictMap.forEach((districts, costCentreId) => {
                this.costCentreToDistrictMap.set(
                    costCentreId,
                    Array.from(districts).sort((a, b) =>
                        a.name.localeCompare(b.name)
                    )
                );
            });

            DistrictsToCostCentreMap.forEach((costCentres, districtId) => {
                this.districtToCostCentreMap.set(
                    districtId,
                    Array.from(costCentres).sort((a, b) =>
                        a.name.localeCompare(b.name)
                    )
                );
            });
        } else {
            data.forEach((item) => {
                if (item?.costcenterid && item?.costcentername) {
                    costCentreMap.set(
                        item.costcenterid,
                        item.costcentername.trim()
                    );
                }
                if (item?.clientid && item?.clientname) {
                    clientMap.set(item.clientid, item.clientname.trim());
                }
                if (item?.locationid && item?.locationname) {
                    locationMap.set(item.locationid, item.locationname.trim());
                }

                // Cost Centre → Districts
                if (item?.costcenterid && item?.clientid && item?.clientname) {
                    if (!CostCentresToDistrictMap.has(item.costcenterid)) {
                        CostCentresToDistrictMap.set(
                            item.costcenterid,
                            new Set()
                        );
                    }
                    CostCentresToDistrictMap.get(item.costcenterid)?.add({
                        id: item.clientid,
                        name: item.clientname.trim()
                    });
                }

                // District → Cost Centres
                if (
                    item?.clientid &&
                    item?.costcenterid &&
                    item?.costcentername
                ) {
                    if (!DistrictsToCostCentreMap.has(item.clientid)) {
                        DistrictsToCostCentreMap.set(item.clientid, new Set());
                    }
                    DistrictsToCostCentreMap.get(item.clientid)?.add({
                        id: item.costcenterid,
                        name: item.costcentername.trim()
                    });
                }
            });

            this.costCentreOptions = Array.from(costCentreMap.entries())
                .map(([id, name]) => ({ id, name }))
                .sort((a, b) => a.name.localeCompare(b.name));

            this.districtvalues = Array.from(clientMap.entries())
                .map(([id, name]) => ({ id, name }))
                .sort((a, b) => a.name.localeCompare(b.name));

            this.locationOptions = Array.from(locationMap.entries())
                .map(([id, name]) => ({ id, name }))
                .sort((a, b) => a.name.localeCompare(b.name));

            // Convert Set to Array for mappings
            CostCentresToDistrictMap.forEach((districts, costCentreId) => {
                this.costCentreToDistrictMap.set(
                    costCentreId,
                    Array.from(districts).sort((a, b) =>
                        a.name.localeCompare(b.name)
                    )
                );
            });

            DistrictsToCostCentreMap.forEach((costCentres, districtId) => {
                this.districtToCostCentreMap.set(
                    districtId,
                    Array.from(costCentres).sort((a, b) =>
                        a.name.localeCompare(b.name)
                    )
                );
            });
        }

        this.filteredDistrictOptions = [...this.costCentreOptions];
        this.filteredClientValues = [...this.districtvalues];
    }

    onDistrictChange(selectedValues: any[]) {
        this.selectedDistricts = selectedValues;

        if (this.selectedDistricts.length === 0) {
            // Reset client values if no districts are selected
            this.filteredClientValues = [...this.districtvalues];
        } else {
            // Filter clients based on selected districts
            const filteredDistricts = new Map<string, any>();
            if (!this.onlyClientUser) {
                this.selectedDistricts.forEach((costCentreId) => {
                    if (this.costCentreToDistrictMap.has(costCentreId)) {
                        this.costCentreToDistrictMap
                            .get(costCentreId)
                            ?.forEach((district) => {
                                filteredDistricts.set(district.id, district); // Ensure unique districts
                            });
                    }
                });
                // Convert the Map to an array for filtered client values
                this.filteredClientValues = Array.from(filteredDistricts.values());
            }

        }
    }

    onClientChange(selectedValues: any[]) {
        this.selectedClient = selectedValues;

        if (this.selectedClient.length === 0) {
            // Reset district options if no clients are selected
            this.filteredDistrictOptions = [...this.costCentreOptions];
        } else {
            // Filter cost centres based on selected clients
            const filteredCostCentres = new Map<string, any>();
            if (this.onlyClientUser) {
                this.selectedClient.forEach((districtId) => {
                    if (this.districtToCostCentreMap.has(districtId)) {
                        this.districtToCostCentreMap
                            .get(districtId)
                            ?.forEach((costCentre) => {
                                filteredCostCentres.set(costCentre.id, costCentre); // Ensure unique cost centres
                            });
                    }
                });
                // Convert the Map to an array for filtered district options
                this.filteredDistrictOptions = Array.from(
                    filteredCostCentres.values()
                );
            }
        }
    }

    ondisplayResults() {
        // Fetch locations and save filters
        this.fetchLocations();
        this.saveFilters();
        const tagBox = document.querySelector(
            '#clientSelectBox input'
        ) as HTMLInputElement;
        if (tagBox) tagBox.value = '';

        // Trigger totals and dashboard updates
        setTimeout(() => {
            this.refreshGridTotals();
            this.syncDashboardWithTable();
        }, 300);
    }
    onclearDropdowns() {
        this.selectedDistricts = [];
        this.selectedClient = [];
    }

    fetchLocations() {
        if (
            !Array.isArray(this.selectedDistricts) ||
            !Array.isArray(this.selectedClient)
        ) {
            return;
        }

        if (
            this.selectedDistricts.length === 0 &&
            this.selectedClient.length === 0
        ) {
            // Clear data if no filters are selected
            this.filteredLocations = [];
            this.selectedLocationIds = [];
            this.tableData = [];
            this.dynamicLocationData = [];
            this.chartOptions = [];
            this.gaugeOptions = {
                value: 0,
                subvalueIndicator: { type: 'textCloud' },
                rangeContainer: { ranges: [] }
            };
            this.recommendationChartOptions = [];
            this.saveFilters();
            return;
        }

        // Filter locations based on selected districts and clients
        const filteredLocations = this.clientcostcentre?.filter((item) => {
            const matchesCostCentre =
                this.selectedDistricts.length === 0 ||
                this.selectedDistricts.includes(item.costcenterid);
            const matchesClient =
                this.selectedClient.length === 0 ||
                this.selectedClient.includes(item.clientid);
            return matchesCostCentre && matchesClient;
        });

        if (!filteredLocations || filteredLocations.length === 0) {
            // Clear data if no locations match the filters
            this.filteredLocations = [];
            this.selectedLocationIds = [];
            this.tableData = [];
            this.dynamicLocationData = [];
            this.chartOptions = [];
            this.gaugeOptions = {
                value: 0,
                subvalueIndicator: { type: 'textCloud' },
                rangeContainer: { ranges: [] }
            };
            this.recommendationChartOptions = [];
            this.saveFilters();
            return;
        }

        // Update filtered locations and selected location IDs
        this.filteredLocations = filteredLocations.map((item) => ({
            id: item.locationid,
            name: item.locationname
        }));

        this.selectedLocationIds = this.filteredLocations.map((loc) => loc.id);

        this.filterDataBasedOnLocation(this.selectedLocationIds);
        this.onLocationChange(this.selectedLocationIds);
        this.saveFilters();

        setTimeout(() => {
            this.refreshGridTotals();
            this.syncDashboardWithTable();
        }, 300);
    }

    dynamicLocationData: any[] = [];
    onLocationChange(selectedLocationIds: any[]) {
        if (!selectedLocationIds || selectedLocationIds.length === 0) {
            // Clear the dashboard data when no locations are selected
            this.dynamicLocationData = [];
            this.chartOptions = [];
            this.gaugeOptions = {
                value: 0,
                subvalueIndicator: { type: 'textCloud' },
                rangeContainer: { ranges: [] }
            };
            this.recommendationChartOptions = [];
            this.saveFilters(); // Save the cleared state
            return;
        }

        // Existing logic for handling selected locations
        this.dynamicLocationData = selectedLocationIds.map((locationId) => {
            const location = this.getLocationNameById(locationId);
            const complianceScore = this.calculateComplianceScore(locationId);
            const inspectionChartData =
                this.getInspectionDataByLocation(locationId);
            const recommendationChartData =
                this.getRecommendationDataByLocation(locationId);
            return {
                location,
                locationId,
                complianceScore,
                inspectionChartData,
                recommendationChartData
            };
        });

        const startIndex = this.pageNumber * this.recordsPerPage;
        const endIndex = startIndex + this.recordsPerPage;

        if (this.dynamicLocationData && this.dynamicLocationData.length > 0) {
            this.dynamicLocationData = this.dynamicLocationData.slice(
                startIndex,
                endIndex
            );
        }

        this.saveFilters();

        if (this.dynamicLocationData.length > 0) {
            this.chartOptions = this.dynamicLocationData.map((data) => ({
                location: data.location,
                dataSource: data.inspectionChartData || [],
                series: [
                    { valueField: 'Overdue', name: 'Overdue' },
                    { valueField: 'DueThisYear', name: 'Due This Year' },
                    { valueField: 'DueNextYear', name: 'Due Next Year' }
                ],
                argumentField: 'category'
            }));

            this.gaugeOptions = {
                value: this.dynamicLocationData[0]?.complianceScore || 0,
                subvalueIndicator: { type: 'textCloud' },
                rangeContainer: {
                    ranges: this.getGaugeRanges(
                        this.dynamicLocationData[0]?.complianceScore || 0
                    )
                }
            };

            this.recommendationChartOptions = this.dynamicLocationData.map(
                (data) => ({
                    location: data.location,
                    dataSource: data.recommendationChartData || [],
                    series: [{ valueField: 'value', name: 'Value' }],
                    argumentField: 'category'
                })
            );
        }
    }

    getGaugeRanges(score: number) {
        return [
            // **🔥 Smooth Red Gradient (0-50)**
            { startValue: 0, endValue: 5, color: '#300000' }, // Almost Black-Red
            { startValue: 5, endValue: 10, color: '#550000' }, // Blood Red
            { startValue: 10, endValue: 15, color: '#770101' }, // Deep Ruby
            { startValue: 15, endValue: 20, color: '#960202' }, // Dark Crimson
            { startValue: 20, endValue: 25, color: '#B11204' }, // Rich Red
            { startValue: 25, endValue: 30, color: '#CB2206' }, // Ember Red
            { startValue: 30, endValue: 35, color: '#E03C10' }, // Burnt Orange-Red
            { startValue: 35, endValue: 40, color: '#F35A1A' }, // Dark Orange
            { startValue: 40, endValue: 45, color: '#FB7A22' }, // Bright Orange
            { startValue: 45, endValue: 50, color: '#FFA333' }, // Soft Amber

            // **🌟 Smooth Yellow Gradient (50-90)**
            { startValue: 50, endValue: 55, color: '#FFF14C' }, // Neon Yellow
            { startValue: 55, endValue: 60, color: '#FFE83D' }, // Bright Lemon
            { startValue: 60, endValue: 65, color: '#FDDC2E' }, // Vivid Lemon
            { startValue: 65, endValue: 70, color: '#F2D01F' }, // Golden Lemon
            { startValue: 70, endValue: 75, color: '#E8C30F' }, // Deep Gold Yellow
            { startValue: 75, endValue: 80, color: '#D9B400' }, // Strong Golden
            { startValue: 80, endValue: 85, color: '#CBA300' }, // Deep Gold
            { startValue: 85, endValue: 90, color: '#B89600' }, // Dark Mustard

            // **🌿 Smooth Green Gradient (90-100) (Reversed Order)**
            { startValue: 90, endValue: 92, color: '#00D86E' }, // Bright Natural Green
            { startValue: 92, endValue: 94, color: '#00BE5C' }, // Vibrant Leaf Green
            { startValue: 94, endValue: 96, color: '#00A84B' }, // Rich Grass Green
            { startValue: 96, endValue: 98, color: '#00953C' }, // Fresh Emerald
            { startValue: 98, endValue: 100, color: '#007A2F' } // Deep Jungle Green
        ];
    }

    getLocationNameById(locationId) {
        const location = this.tableData.find(
            (entry) => entry.locationId === locationId
        );
        return location ? location.location : 'Unknown';
    }

    calculateComplianceScore(locationId: number): number {
        const locationData = this.tableData.find(
            (data) => data.locationId === locationId
        );

        const score =
            locationData && locationData.totalAssets > 0
                ? parseFloat(
                    (
                        (100 / locationData.totalAssets) *
                        locationData.currentInspections
                    ).toFixed(2)
                )
                : 0;

        return score;
    }
    customizeText(arg: any): string {
        const valuesToShow = [0, 20, 50, 100];
        return valuesToShow.includes(arg.value) ? `${arg.value}` : '';
    }
    customizeComplianceScoreText() {
        return `${this.locationData.complianceScore.toFixed(2)}%`;
    }

    getInspectionDataByLocation(locationId: number, filter?: any[]) {
        let assetIds: string[] = [];

        // Extract asset IDs if filter is provided
        if (filter && filter.length === 3 && filter[0] === 'assetid') {
            assetIds = filter[2]; // ✅ Extracting asset IDs
        }

        // Filter inspections based on location and assetId
        const inspections = this._inspections?.filter(
            (inspection) =>
                (inspection.locationid === locationId ||
                    inspection.locationId === locationId) &&
                (assetIds.length === 0 || assetIds.includes(inspection.assetid)) // ✅ Filter by asset ID if available
        );

        // ✅ Collect asset IDs per category
        const overdueAssetIds = inspections
            ?.filter((insp) => insp.status === 'Overdue')
            .map((insp) => insp.assetid);

        const dueThisYearAssetIds = inspections
            ?.filter((insp) => insp.status === 'Due This Year')
            .map((insp) => insp.assetid);

        const dueNextYearAssetIds = inspections
            ?.filter((insp) => insp.status === 'Due Next Year')
            .map((insp) => insp.assetid);

        return [
            {
                type: 'Overdue',
                locationId,
                Vessels: this.getInspectionCategoryData(inspections, 'Vessels')
                    .Overdue,
                Piping: this.getInspectionCategoryData(inspections, 'Piping')
                    .Overdue,
                Tanks: this.getInspectionCategoryData(inspections, 'Tanks')
                    .Overdue,
                overdueAssetIds // ✅ Include asset IDs
            },
            {
                type: 'Due This Year',
                locationId,
                Vessels: this.getInspectionCategoryData(inspections, 'Vessels')
                    .DueThisYear,
                Piping: this.getInspectionCategoryData(inspections, 'Piping')
                    .DueThisYear,
                Tanks: this.getInspectionCategoryData(inspections, 'Tanks')
                    .DueThisYear,
                dueThisYearAssetIds // ✅ Include asset IDs
            },
            {
                type: 'Due Next Year',
                locationId,
                Vessels: this.getInspectionCategoryData(inspections, 'Vessels')
                    .DueNextYear,
                Piping: this.getInspectionCategoryData(inspections, 'Piping')
                    .DueNextYear,
                Tanks: this.getInspectionCategoryData(inspections, 'Tanks')
                    .DueNextYear,
                dueNextYearAssetIds // ✅ Include asset IDs
            }
        ];
    }

    getInspectionCategoryData(inspections: any[], category: string) {
        return {
            Overdue: inspections?.filter(
                (ins) =>
                    ins?.inspectionassetcategory === category &&
                    ins.nextinspectiondue &&
                    ins.schedulestatus.trim() === 'Active' &&
                    ins.scheduletype !== null && // ✅ Excludes null schedule types
                    ins.assetstatus !== 'Removed from Unit' &&
                    new Date(ins.nextinspectiondue) < this.currentdate
            ).length,

            DueThisYear: inspections?.filter(
                (ins) =>
                    ins?.inspectionassetcategory === category && // ✅ Matches the given category
                    ins.nextinspectiondue && // ✅ Ensures next inspection date exists
                    ins.schedulestatus.trim() === 'Active' && // ✅ Only 'Active' schedules
                    ins.scheduletype !== null && // ✅
                    //  Excludes null schedule types
                    ins.assetstatus !== 'Removed from Unit' && // ✅ Excludes out-of-service assets
                    new Date(ins.nextinspectiondue).getFullYear() ===
                    new Date().getFullYear() && // ✅ Due in the current year
                    new Date(ins.nextinspectiondue).getTime() >=
                    new Date().setHours(0, 0, 0, 0) // ✅ Only future inspections (today onwards)
            ).length,

            DueNextYear: inspections?.filter(
                (ins) =>
                    ins?.inspectionassetcategory === category &&
                    ins.nextinspectiondue &&
                    ins.schedulestatus.trim() === 'Active' &&
                    new Date(ins.nextinspectiondue).getFullYear() ===
                    this.currentdate.getFullYear() + 1
            ).length
        };
    }

    getRecommendationDataByLocation(locationId: number) {
        if (!this.tableData || !Array.isArray(this.tableData)) {
            return [
                { category: 'P4 (Monitor)', value: 0, locationId },
                { category: 'P5 (Minor)', value: 0, locationId },
                { category: 'P6 (Major)', value: 0, locationId }
            ];
        }

        const locationData = this.tableData.find(
            (data) => data.locationId === locationId
        );

        if (!locationData)
            return [
                { category: 'P4 (Monitor)', value: 0, locationId },
                { category: 'P5 (Minor)', value: 0, locationId },
                { category: 'P6 (Major)', value: 0, locationId }
            ];

        return [
            {
                category: 'P4 (Monitor)',
                value: locationData.openMonitors || 0,
                locationId
            },
            {
                category: 'P5 (Minor)',
                value: locationData.openMinors || 0,
                locationId
            },
            {
                category: 'P6 (Major)',
                value: locationData.openMajors || 0,
                locationId
            }
        ];
    }

    gaugeClicked(locationData: any) {
        const locationId =
            locationData?.locationId ||
            locationData?.inspectionChartData?.[0]?.locationId ||
            locationData?.recommendationChartData?.[0]?.locationId;

        if (!locationId) return;

        const filteredAssetIds = this.currentInspections
            ? [
                ...new Set(
                    this.currentInspections
                        .filter((asset) => asset.locationid === locationId)
                        .map((asset) => String(asset.assetidname))
                )
            ]
            : [];
        const breadcrumbLabel = 'Current Inspections';
        const appendLocationId = locationId.toString().slice(-4);
        localStorage.setItem(
            `selectedSite${appendLocationId}`, locationId
        );
        sessionStorage.setItem('breadcrumbLabel', breadcrumbLabel);
        sessionStorage.setItem(
            'assetObjIds',
            JSON.stringify(filteredAssetIds)
        );
        const path = `#/aimaas/drilldown?fromOverview=${breadcrumbLabel}?locationId=${appendLocationId}`;
        window.open(path, '_blank');
        // this._router.navigate(['/aimaas/drilldown'], {
        //     state: {
        //         source: 'overview',
        //         breadCrumbLabel: 'Current Inspections',
        //         data: {
        //             assetObjIds: filteredAssetIds
        //         }
        //     }
        // });
    }

    navigateToDrilldown(event: any) {
        const clickedData = event.target?.data || event.point?.data;
        const clickedValue = event.target?.value; // Value of the clicked bar
        let seriesName = '';

        if (!clickedData || !clickedData.locationId || !clickedData.type) {
            return;
        }

        // 🔹 Identify the clicked series by matching its value
        if (clickedData.Vessels === clickedValue) {
            seriesName = 'Vessels';
        } else if (clickedData.Piping === clickedValue) {
            seriesName = 'Piping';
        } else if (clickedData.Tanks === clickedValue) {
            seriesName = 'Tanks';
        } else {
            return;
        }
        localStorage.setItem('selectedSite', clickedData.locationId);
        let currentFilter = this.getInspectionFilter(clickedData, seriesName);
        let breadcrumbLabel = '';
        if (clickedData.type === 'Overdue')
            breadcrumbLabel = `${seriesName} - Overdue`;
        else if (clickedData.type === 'Due This Year')
            breadcrumbLabel = `${seriesName} - Due This Year`;
        else if (clickedData.type === 'Due Next Year')
            breadcrumbLabel = `${seriesName} - Due Next Year`;
        const appendLocationId = clickedData.locationId.toString().slice(-4);
        localStorage.setItem(
            `selectedSite${appendLocationId}`, clickedData.locationId
        );
        sessionStorage.setItem('breadcrumbLabel', breadcrumbLabel);
        sessionStorage.setItem(
            'currentFilter',
            JSON.stringify(currentFilter)
        );
        const path = `#/aimaas/inspection-drilldown?fromOverview=${breadcrumbLabel}?locationId=${appendLocationId}`;
        window.open(path, '_blank');
    }

    navigateToAnomalyDrilldown(event: any) {
        const clickedData = event.target?.data || event.point?.data;
        let category = '';

        if (!clickedData || !clickedData.category || !clickedData.value) {
            return;
        }
        localStorage.setItem('selectedSite', clickedData.locationId);
        // 🔹 Identify the clicked priority category using partial match
        if (clickedData.category.includes('P4')) {
            category = 'P4';
        } else if (clickedData.category.includes('P5')) {
            category = 'P5';
        } else if (clickedData.category.includes('P6')) {
            category = 'P6';
        } else {
            return;
        }

        const currentFilter = [
            ['resolutionstate', '=', 'Recommended'], // Static filter for 'Recommended'
            'and',
            ['anomalypriority', 'contains', category.slice(1)] // e.g., '4', '5', or '6'
        ];
        let breadcrumbLabel = '';
        if (clickedData.type === 'Overdue')
            breadcrumbLabel = `${category} - Open Recommendations`;
        else if (clickedData.type === 'Due This Year')
            breadcrumbLabel = `${category} - Open Recommendations`;
        else if (clickedData.type === 'Due Next Year')
            breadcrumbLabel = `${category} - Open Recommendations`;
        const appendLocationId = clickedData.locationId.toString().slice(-4);
        localStorage.setItem(
            `selectedSite${appendLocationId}`, clickedData.locationId
        );
        sessionStorage.setItem('breadcrumbLabel', breadcrumbLabel);
        sessionStorage.setItem(
            'currentFilter',
            JSON.stringify(currentFilter)
        );
        const path = `#/aimaas/anomaly-drilldown?fromOverview=${breadcrumbLabel}?locationId=${appendLocationId}`;
        window.open(path, '_blank');
    }

    getAnomalyRecommendationFilter(clickedData: any, category: string) {
        let anomalyFilter: any = [];
        let status = '';

        // Determine if the category is Open or Closed
        if (clickedData.category.includes('Open')) {
            status = 'Open';
        } else if (clickedData.category.includes('Closed')) {
            status = 'Closed';
        }

        // Determine the filter based on the status
        let filterValues: string | string[] = '';
        if (status === 'Open') {
            filterValues = 'Recommended'; // For open anomalies
        } else if (status === 'Closed') {
            filterValues = ['Resolved', 'Rejected']; // For closed anomalies
        }
        // 🔹 Determine the priority filter
        let priorityFilter;
        if (category === 'P4') {
            priorityFilter = ['anomalypriority', 'contains', '4'];
        } else if (category === 'P5') {
            priorityFilter = ['anomalypriority', 'contains', '5'];
        } else {
            priorityFilter = ['anomalypriority', 'contains', '6'];
        }

        // 🔹 Construct the final filter
        if (Array.isArray(filterValues)) {
            anomalyFilter = [
                [
                    ['resolutionstate', 'contains', filterValues[0]],
                    'or',
                    ['resolutionstate', 'contains', filterValues[1]]
                ],
                'and',
                priorityFilter
            ];
        } else {
            anomalyFilter = [
                ['resolutionstate', '=', filterValues],
                'and',
                priorityFilter
            ];
        }

        return anomalyFilter;
    }

    getInspectionFilter(clickedData: any, category: string) {
        let currentFilter: any = [];
        const currentYear = new Date().getFullYear();
        switch (clickedData.type) {
            case 'Overdue':
                currentFilter = [
                    ['nextinspectiondue', '<', new Date()],
                    'and',
                    ['schedulestatus', '=', 'Active'],
                    'and',
                    ['inspectionassetcategory', '=', category],
                    'and',
                    ['Out of Service']
                ];
                break;

            case 'Due This Year':
                const now = new Date();
                now.setHours(0, 0, 0, 0);

                currentFilter = [
                    ['nextinspectiondue', '>=', new Date(currentYear, 0, 1)], // 1️⃣ Inspections due from Jan 1st of the current year
                    'and',
                    ['nextinspectiondue', '<', new Date(currentYear + 1, 0, 1)], // 2️⃣ Inspections due before Jan 1st of the next year
                    'and',
                    ['nextinspectiondue', '>=', now], // 3️⃣ Ensures that only upcoming inspections are considered
                    'and',
                    ['schedulestatus', '=', 'Active'],
                    ['inspectionassetcategory', '=', category]
                ];

                // ✅ Include specific inspection asset category if needed
                if (this.dueThisYearInspections.selectedValues?.length > 0) {
                    currentFilter.push('and', [
                        'inspectionassetcategory',
                        'anyof',
                        this.dueThisYearInspections.selectedValues
                    ]);
                }

                // ✅ Include specific schedule type if applicable
                if (this.dueThisYearInspections.scheduletype) {
                    currentFilter.push('and', [
                        'scheduletype',
                        '=',
                        this.dueThisYearInspections.scheduletype === 'null'
                            ? null
                            : this.dueThisYearInspections.scheduletype
                    ]);
                }
                if (!this.isincludeoutofservicechecked) {
                    currentFilter.push('and', [
                        'assetstatus',
                        'noneof',
                        ['Removed from Unit', 'Out of Service']
                    ]);
                }
                break;

            case 'Due Next Year':

                currentFilter = [
                    [
                        'nextinspectiondue',
                        '>=',
                        new Date(currentYear + 1, 0, 1)
                    ],
                    'and',
                    [
                        'nextinspectiondue',
                        '<',
                        new Date(currentYear + 2, 0, 1)
                    ],
                    'and',
                    ['schedulestatus', '=', 'Active']
                ];

                // ✅ Include specific inspection asset category if needed
                if (this.dueThisYearInspections.selectedValues?.length > 0) {
                    currentFilter.push('and', [
                        'inspectionassetcategory',
                        'anyof',
                        this.dueThisYearInspections.selectedValues
                    ]);
                }

                // ✅ Include specific schedule type if applicable
                if (this.dueThisYearInspections.scheduletype) {
                    currentFilter.push('and', [
                        'scheduletype',
                        '=',
                        this.dueThisYearInspections.scheduletype === 'null'
                            ? null
                            : this.dueThisYearInspections.scheduletype
                    ]);
                }

                break;

            default:
                return [];
        }

        return currentFilter;
    }

    updateBreadcrumbs(locationId?: number) {
        this.crumbs = [
            {
                label: 'Overview Dashboard',
                route: '/aimaas/overview-dashboard'
            },
            { label: 'Inspection', route: '/aimaas/inspection-drilldown' },
            { label: 'Recommendations', route: '/aimaas/anomaly-drilldown' }
        ];

        if (locationId) {
            const selectedSite = this.availableSites?.find(
                (site) => site.locationid === locationId
            );
            const locationLabel = selectedSite
                ? selectedSite.locationname
                : 'Inspection';

            this.crumbs.push({
                label: locationLabel,
                route: `/aimaas/inspection-drilldown?locationId=${locationId}`
            });
        }
        this.isLoading = false;
    }

    updateInspectionDataBasedOnLocation() {
        const inspectionMap = new Map<number, any[]>();

        this._inspections?.forEach((inspection) => {
            if (inspection.scheduletype != null) {
                if (!inspectionMap.has(inspection.assetid)) {
                    inspectionMap.set(inspection.assetid, []);
                }
                inspectionMap.get(inspection.assetid)?.push(inspection);
            }
        });

        const currentdate = new Date();

        this.assetsKPI?.forEach((asset) => {
            const inspectionsOfAsset = inspectionMap.get(asset.id) ?? [];

            const overdue = inspectionsOfAsset.filter(
                (inspection) =>
                    inspection.nextinspectiondue != null &&
                    currentdate > new Date(inspection.nextinspectiondue)
            );

            const current = inspectionsOfAsset.filter(
                (inspection) =>
                    inspection.nextinspectiondue != null &&
                    currentdate < new Date(inspection.nextinspectiondue)
            );

            const noScheduled = inspectionsOfAsset.filter(
                (inspection) => inspection.nextinspectiondue == null
            );

            if (overdue.length > 0) {
                this.overdueInspections.push(...overdue);
            } else if (current.length > 0) {
                this.currentInspections.push(...current);
            } else if (noScheduled.length > 0) {
                this.noInspections.push(...noScheduled);
            }
        });
        const filteredInspections = this._inspections?.filter(
            (inspection) => inspection.scheduletype != null
        );
        const assetIds = filteredInspections?.map(
            (inspection) => inspection.assetid
        );
        this.totalAssets = this.assetsKPI?.filter((asset) =>
            assetIds?.includes(String(asset.id))
        );
        sessionStorage.setItem('inspectionsDataStatus', 'true');
    }

    onRowClick(event: any): void {
        if (event?.data?.locationId) {
            const clickedCell = event.event.target.closest('td');
            const clickedRow = clickedCell.closest('tr');
            const cellIndex = Array.from(clickedRow.children).indexOf(
                clickedCell
            );
            const column = event.columns[cellIndex];
            const columnField = column.dataField;
            const cellValue = event.data[columnField]; // Get the value of the clicked cell
            const allRows = document.querySelectorAll('tr.highlighted-row');
            allRows.forEach((row) => {
                const htmlRow = row as HTMLElement;
                htmlRow.classList.remove('highlighted-row');
                htmlRow.style.backgroundColor = ''; // Reset inline style
            });
            // Remove underline from previously clicked cells
            const allCells = document.querySelectorAll('td.highlighted-cell');
            allCells.forEach((cell) => {
                const htmlCell = cell as HTMLElement; // Cast to HTMLElement
                htmlCell.classList.remove(
                    'highlighted-cell',
                    'underlined-cell'
                );
                htmlCell.style.textDecoration = '';
                htmlCell.style.backgroundColor = ''; // Reset inline style
            });
            // Highlight the clicked row
            if (clickedRow) {
                const htmlRow = clickedRow as HTMLElement;
                htmlRow.classList.add('highlighted-row');
                htmlRow.style.backgroundColor = ' #b8e2f4'; // Debugging inline style
            }
            if (clickedCell) {
                const htmlClickedCell = clickedCell as HTMLElement; // Cast to HTMLElement
                htmlClickedCell.classList.add(
                    'highlighted-cell',
                    'underlined-cell'
                );
                htmlClickedCell.style.textDecoration =
                    cellValue > 0 ? 'underline' : ''; // Debugging inline style
                htmlClickedCell.style.backgroundColor = '#8dcfec'; // Debugging inline style
            }
            // Check if the cell value is greater than 0
            if (cellValue <= 0) {
                return; // Exit the function if the value is not greater than 0
            }
            let columnGroup = '';

            if (
                [
                    'currentInspections',
                    'overdueInspections',
                    'noInspections'
                ].includes(columnField)
            ) {
                columnGroup = 'Equipments';
            } else if (
                ['overdue', 'dueThisYear', 'dueNextYear'].includes(columnField)
            ) {
                columnGroup = 'Inspections';
            } else if (
                ['openMajors', 'openMinors', 'openMonitors'].includes(
                    columnField
                )
            ) {
                columnGroup = 'Recommendations';
            } else if (['totalAssets'].includes(columnField)) {
                columnGroup = 'TotalAssets';
            } else {
                columnGroup = 'Overview Dashboard';
            }

            let path = '';
            const appendLocationId = event.data.locationId.toString().slice(-4);
            localStorage.setItem(
                `selectedSite${appendLocationId}`,
                event.data.locationId
            );
            // ✅ Set breadcrumb label for each category
            let breadcrumbLabel = ''; // Default label
            let assetIds = [];
            let filter: any;
            let priority = '';
            let filterValue: any;
            const currentYear = new Date().getFullYear();
            switch (columnField) {
                case 'currentInspections':
                    breadcrumbLabel = 'Assets with Current Inspections';
                    assetIds = this.currentInspections
                        ? [
                            ...new Set(
                                this.currentInspections
                                    .filter(
                                        (asset) =>
                                            asset.locationid ===
                                            event.data.locationId
                                    )
                                    .map((asset) => String(asset.assetidname))
                            )
                        ]
                        : [];
                    sessionStorage.setItem(
                        'assetObjIds',
                        JSON.stringify(assetIds)
                    );
                    break;
                case 'overdueInspections':
                    breadcrumbLabel = 'Assets with Overdue Inspections';
                    assetIds = this.overdueInspections
                        ? [
                            ...new Set(
                                this.overdueInspections
                                    .filter(
                                        (asset) =>
                                            asset.locationid ===
                                            event.data.locationId
                                    )
                                    .map((asset) => String(asset.assetidname))
                            )
                        ]
                        : [];
                    sessionStorage.setItem(
                        'assetObjIds',
                        JSON.stringify(assetIds)
                    );
                    break;

                case 'noInspections':
                    breadcrumbLabel = 'Assets with No Inspections';
                    assetIds = this.noInspections
                        ? [
                            ...new Set(
                                this.noInspections
                                    .filter(
                                        (asset) =>
                                            asset.locationid ===
                                            event.data.locationId
                                    )
                                    .map((asset) => String(asset.assetidname))
                            )
                        ]
                        : [];
                    sessionStorage.setItem(
                        'assetObjIds',
                        JSON.stringify(assetIds)
                    );
                    break;

                case 'overdue':
                    breadcrumbLabel = 'Overdue Inspections';
                    filter = [
                        ['nextinspectiondue', '<', new Date()],
                        'and',
                        ['schedulestatus', '=', 'Active']
                    ];
                    sessionStorage.setItem(
                        'currentFilter',
                        JSON.stringify(filter)
                    );
                    break;
                case 'dueThisYear':
                    breadcrumbLabel = 'Inspections Due This Year';
                    const now = new Date();
                    now.setHours(0, 0, 0, 0);
                    filter = [
                        [
                            'nextinspectiondue',
                            '>=',
                            new Date(currentYear, 0, 1)
                        ],
                        'and',
                        [
                            'nextinspectiondue',
                            '<',
                            new Date(currentYear + 1, 0, 1)
                        ],
                        'and',
                        ['nextinspectiondue', '>=', now],
                        'and',
                        ['schedulestatus', '=', 'Active']
                    ];
                    sessionStorage.setItem(
                        'currentFilter',
                        JSON.stringify(filter)
                    );
                    break;
                case 'dueNextYear':
                    breadcrumbLabel = 'Inspections Due Next Year';
                    filter = [
                        [
                            'nextinspectiondue',
                            '>=',
                            new Date(currentYear + 1, 0, 1)
                        ],
                        'and',
                        [
                            'nextinspectiondue',
                            '<',
                            new Date(currentYear + 2, 0, 1)
                        ],
                        'and',
                        ['schedulestatus', '=', 'Active']
                    ];
                    sessionStorage.setItem(
                        'currentFilter',
                        JSON.stringify(filter)
                    );
                    break;
                case 'openMajors':
                    breadcrumbLabel = 'P6 Priorities Open';
                    priority = 'P6';
                    var priorityFilter;
                    priorityFilter = ['anomalypriority', 'contains', '6'];
                    filterValue = [
                        ['resolutionstate', '=', 'Recommended'],
                        'and',
                        priorityFilter
                    ];
                    sessionStorage.setItem(
                        'currentFilter',
                        JSON.stringify(filterValue)
                    );
                    break;
                case 'openMinors':
                    breadcrumbLabel = 'P5 Priorities Open';
                    priority = 'P5';
                    var priorityFilter;
                    priorityFilter = ['anomalypriority', 'contains', '5'];
                    filterValue = [
                        ['resolutionstate', '=', 'Recommended'],
                        'and',
                        priorityFilter
                    ];
                    sessionStorage.setItem(
                        'currentFilter',
                        JSON.stringify(filterValue)
                    );
                    break;
                case 'openMonitors':
                    breadcrumbLabel = 'P4 Priorities Open';
                    priority = 'P4';
                    var priorityFilter;
                    priorityFilter = ['anomalypriority', 'contains', '4'];
                    filterValue = [
                        ['resolutionstate', '=', 'Recommended'],
                        'and',
                        priorityFilter
                    ];
                    sessionStorage.setItem(
                        'currentFilter',
                        JSON.stringify(filterValue)
                    );
                    break;
                case 'totalAssets':
                    breadcrumbLabel = 'Total Assets';
                    sessionStorage.setItem(
                        'assetObjIds',
                        JSON.stringify(
                            this.totalAssets
                                ?.filter(
                                    (asset) =>
                                        asset.locationid ===
                                        event.data.locationId
                                )
                                ?.map((asset) => String(asset.assetid)) || []
                        )
                    );
                    break;
            }

            // ✅ Store breadcrumb label & source in sessionStorage
            sessionStorage.setItem('breadcrumbLabel', breadcrumbLabel);
            sessionStorage.setItem('source', 'overview');

            // ✅ Update history state for consistency
            history.pushState(
                {
                    source: 'overview',
                    breadCrumbLabel: breadcrumbLabel,
                    locationId: event.data.locationId, // 👈 Pass locationId directly here
                    assetObjIds: assetIds, // optional
                    currentFilter: filter || filterValue // optional
                },
                '',
                path
            );
            // ✅ Assign paths based on columnGroup
            if (columnGroup === 'Equipments') {
                path = `#/aimaas/drilldown?fromOverview=${breadcrumbLabel}?locationId=${appendLocationId}`;
            } else if (columnGroup === 'Inspections') {
                path = `#/aimaas/inspection-drilldown?fromOverview=${breadcrumbLabel}?locationId=${appendLocationId}`;
            } else if (columnGroup === 'Recommendations') {
                path = `#/aimaas/anomaly-drilldown?fromOverview=${breadcrumbLabel}?locationId=${appendLocationId}`;
            } else if (columnGroup === 'TotalAssets') {
                path = `#/aimaas/drilldown?fromOverview=${breadcrumbLabel}?locationId=${appendLocationId}`;
            } else {
                path = '#/aimaas/dashboards';
                const clientdata = this.clientLocationData?.filter(
                    (site) => site.locationid === event.data.locationId
                );
                localStorage.setItem('selectedSite', event.data.locationId);
                localStorage.setItem(
                    'selectedClient',
                    clientdata[0].clientid
                );
                localStorage.setItem(
                    'selectedDistrict',
                    clientdata[0].costcenterid
                );
            }
            window.open(path, '_blank');
        }
    }

    private refreshGridTotals() {
        if (this.dataGrid?.instance) {
            // Force the grid to refresh and recalculate totals
            this.dataGrid.instance.refresh(true);
        } else {
        }
    }
}
