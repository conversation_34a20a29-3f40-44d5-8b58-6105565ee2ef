:host {
    width: 100%;
}

.centered {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.centered {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}
.listpagebuttons {
    margin-left: 5px;
}
.content-block {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    gap: 15px; /* Adjust spacing */
}

.left-section {
    display: flex;
    flex-direction: column;
    width: 35%;
    gap: 10px;
}

.top-row,
.bottom-row {
    display: flex;
    justify-content: space-between;
    gap: 10px;
    width: 100%;
}

#selectBox4,
#selectBox3,
#selectBox2,
#selectBox {
    flex: 1;
    min-width: 65%;
    max-width: 65%;
}

.right-section {
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    align-items: center;
    gap: 0px;
    padding-top: 10px;
    padding-right: 38px;
    flex-wrap: wrap; /* ✨ Enable wrapping for responsive layout */
    overflow-x: auto;
    max-width: 100%;
    margin-top: 35px;
}

.listpagebuttons {
    height: 25px;
    font-size: 12px;
    white-space: nowrap;
    text-align: center;
    padding: 0;
    /* No width set here so they take size from HTML or auto-fit */
}

/* DevExtreme button styling */
dx-button ::ng-deep .dx-button-content {
    padding: 6px 10px !important;
}

dx-button ::ng-deep .dx-button-text {
    font-size: 12px !important;
    font-weight: 500;
    white-space: nowrap;
}

/* ✅ Responsive: show 2 buttons per row on medium screens */
@media (max-width: 1200px) {
    .right-section {
        gap: 10px;
    }

    .listpagebuttons {
        flex: 0 0 48%; /* 2 buttons per row */
    }
}

/* ✅ Stack vertically on small screens */
@media (max-width: 600px) {
    .listpagebuttons {
        flex: 1 1 100%;
    }
}

/* Responsive layout below tablet width */
@media (max-width: 1024px) {
    .right-section {
        width: 100%;
        justify-content: center;
        padding-right: 0;
        margin-top: 15px;
    }

    .listpagebuttons {
        flex: 1 1 45%;
        max-width: 100%;
        margin-bottom: 10px;
    }
}

/* Stack buttons vertically on very small screens */
@media (max-width: 600px) {
    .listpagebuttons {
        flex: 1 1 100%;
    }
}

/* Responsive Fixes */
@media (max-width: 1024px) {
    .content-block {
        flex-direction: column;
        align-items: center;
    }

    .left-section,
    .right-section {
        width: 80%;
        max-width: 100%;
    }

    .top-row,
    .bottom-row {
        flex-direction: column;
    }

    #selectBox4,
    #selectBox3,
    #selectBox2,
    #selectBox {
        width: 70%;
    }
}
