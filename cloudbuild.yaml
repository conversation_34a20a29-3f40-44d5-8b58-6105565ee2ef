steps:
- name: 'gcr.io/cloud-builders/docker'
  id: Build docker image
  secretEnv: ['GITLAB_DEPLOY_USERNAME', 'GITLAB_DEPLOY_TOKEN']
  entrypoint: 'sh'
  args:
  - -xe
  - -c
  - |
    docker build \
    --tag=${_AR_REGISTRY_LOCATION}-docker.pkg.dev/$PROJECT_ID/${_AR_REGISTRY_NAME}/${_IMAGE}:$SHORT_SHA \
    --tag=${_AR_REGISTRY_LOCATION}-docker.pkg.dev/$PROJECT_ID/${_AR_REGISTRY_NAME}/${_IMAGE}:latest \
    --build-arg GITLAB_DEPLOY_USERNAME=$$GITLAB_DEPLOY_USERNAME --build-arg GITLAB_DEPLOY_TOKEN=$$GITLAB_DEPLOY_TOKEN \
    -f ${_DOCKERFILE_PATH} \
    --platform linux/amd64 \
    .

- name: 'gcr.io/cloud-builders/docker'
  id: Push docker image with SHA tag
  args: ['push', '${_AR_REGISTRY_LOCATION}-docker.pkg.dev/$PROJECT_ID/${_AR_REGISTRY_NAME}/${_IMAGE}:$SHORT_SHA']

- name: 'gcr.io/cloud-builders/docker'
  id: Push docker image with latest tag
  args: ['push', '${_AR_REGISTRY_LOCATION}-docker.pkg.dev/$PROJECT_ID/${_AR_REGISTRY_NAME}/${_IMAGE}:latest']

- name: 'google/cloud-sdk:latest'
  id: Trigger Cloud Deploy
  entrypoint: 'sh'
  args:
  - -xe
  - -c
  - |
    gcloud config set deploy/region ${_REGION}
    gcloud deploy apply --file cloud-deploy/${_IMAGE}/${_ENV}/pipeline.yaml
    gcloud deploy apply --file cloud-deploy/${_IMAGE}/${_ENV}/target.yaml
    gcloud deploy releases create ${_IMAGE}-$SHORT_SHA \
                        --delivery-pipeline=${_IMAGE} \
                        --skaffold-file=${_IMAGE}.yaml
availableSecrets:
  secretManager:
  - versionName: projects/$PROJECT_NUMBER/secrets/GITLAB_DEPLOY_USERNAME/versions/latest
    env: 'GITLAB_DEPLOY_USERNAME'
  - versionName: projects/$PROJECT_NUMBER/secrets/GITLAB_DEPLOY_TOKEN/versions/latest
    env: 'GITLAB_DEPLOY_TOKEN'
