# Starter pipeline
# Start with a minimal pipeline that you can customize to build and deploy your code.
# Add steps that build, run tests, deploy, and more:
# https://aka.ms/yaml

trigger:
 branches:
  include:
    - develop
 paths:
   include:
     - api/OrderTracking.API/OrderTracking.API/*

pool:
  vmImage: 'ubuntu-latest'

variables:
  buildConfiguration: 'Release'
  imageRepository: 'ordertrackingapi'
  dockerRegistryServiceConnection: 'KrakenACRConnection'
  containerRegistry: 'krakendev.azurecr.io'
  dockerfilePath: 'api/OrderTracking.API/OrderTracking.API/Dockerfile'
  additionalImageTags: '$(Build.BuildNumber)'
  tag: 'latest'
 
steps:

- task: Docker@2
  displayName: Build and push an image to container registry
  inputs:
   command: buildAndPush
   repository: $(imageRepository)
   dockerfile: $(dockerfilePath)
   containerRegistry: $(dockerRegistryServiceConnection)
   tags: | 
    $(additionalImageTags)
    latest

- task: PublishBuildArtifacts@1
  inputs:    
    pathtoPublish: api/OrderTracking.API/OrderTracking.API/Deployment.yaml
    artifactName: deploymentFile
