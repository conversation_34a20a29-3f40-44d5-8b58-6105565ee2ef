/*  Global Styling */
body {
    font-family: 'Poppins', sans-serif;
    background: linear-gradient(135deg, #cbcfd0, #dfe9f3);
    margin: 0;
    padding: 0;
    color: #333;
}

/*  Selected Cost Centre Container */
.selected-cost-centre-container {
    display: flex;
    align-items: center;
    gap: 15px;
    width: 100% !important;
    padding: 25px 25px;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 15px;
    box-shadow: 5px 5px 15px rgba(0, 0, 0, 0.1),
        -5px -5px 15px rgba(255, 255, 255, 0.5);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease-in-out;
}

.selected-cost-centre-container:hover {
    transform: scale(1.02);
}
.dx-field-value {
    width: 350px; /* Ensure width remains fixed */
    min-height: 40px; /* Set a minimum height */
    max-height: 100px; /* Prevent excessive growth */
    overflow: hidden; /* Hide overflowing content */
    white-space: nowrap; /* Prevent line breaks */
    text-overflow: ellipsis; /* Add '...' when content overflows */
    display: block; /* Ensures proper truncation */
}
.dropdown-container {
    display: flex;
    justify-content: left;
    gap: 18px;
    padding: 20px;
    align-items: center;
    flex-wrap: wrap; /* Prevents content from overflowing */
}

/*  Labels */
.dx-field-item-label {
    font-size: 12px;
    font-weight: 700;
    color: #37474f;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/*  Select Boxes */
#costCentreSelectBox,
#selectBoxOisClient {
    flex-grow: 1;
    max-width: 260px;
    padding: 10px 14px;
    background: rgba(255, 255, 255, 0.6);
    transition: all 0.3s ease-in-out;
}


.dx-tag-box .dx-tag-container {
    max-width: 100%; /* Ensure the tags stay within the container */
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.dx-tag-container::-webkit-scrollbar {
    height: 5px;
}

.dx-tag-container::-webkit-scrollbar-thumb {
    background: #bbb;
    border-radius: 5px;
}

/* Tags */
.dx-tag {
    font-size: 13px;
    background: linear-gradient(135deg, #bbdefb, #64b5f6);
    color: white;
    padding: 6px 15px;
    border-radius: 20px;
    margin: 3px;
    font-weight: 500;
    transition: all 0.3s ease;
}
 
.dx-tag:hover {
    background: linear-gradient(135deg, #64b5f6, #1e88e5);
    cursor: pointer;
}
.container {
    max-width: 100vw; /* Prevent elements from exceeding screen width */
    overflow: hidden;
}

/* Dashboard Container */
.dashboard-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: center; /* Center align */
    align-items: stretch;
    padding: 10px;
    gap: 10px;
    width: 100%;
}
/* Override uppercase transformation for label */
.dx-field-item-label {
    text-transform: none !important;
}

/* Common Styling for Cards */
.chart-container,
.gauge-container {
    flex: 1;
    width: 280px; /* Uniform width */
    height: 300px; /* Consistent height */
    background-color: white;
    box-shadow: 3px 3px 10px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    min-height: 300px; /* Prevent collapsing */
    padding: 15px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center; /* Ensures contents are centered */
}

/* Ensure Circular Gauge Matches Other Components */
.custom-gauge {
    width: 100%;
    height: 220px !important; /* Adjusted height */
    display: flex;
    justify-content: center;
    align-items: center;
}

/* Ensure Gauge Container Aligns Properly */
.gauge-container {
    position: relative;
    text-align: center;
}

/* Compliance Score Display */
.gauge-value {
    position: absolute;
    bottom: 10px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 14px;
    font-weight: bold;
    color: black;
}

/* Chart Styling */
dx-chart {
    width: 100%;
    height: 220px !important; /* Adjusted to match gauge */
    border-radius: 8px;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .chart-container,
    .gauge-container {
        width: 45% !important; /* Two columns on medium screens */
    }
}

@media (max-width: 768px) {
    .chart-container,
    .gauge-container {
        width: 100% !important; /* Full width on small screens */
    }
}

/* Table Container */
.table-container {
    padding: 10px;
    width: 100%;
    // max-width: 1200px; // ✅ Ensures it doesn't exceed the screen width
    background: white;
    border-radius: 10px;
    box-shadow: 2px 2px 10px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: left;
    margin: auto;
    overflow: hidden; // ✅ Prevents horizontal scrolling
}
::ng-deep .dx-datagrid .dx-datagrid-headers.resize-effect {
    transform: scaleX(1.01);
    transition: transform 0.2s ease-in-out;
  }
  
  ::ng-deep .dx-datagrid td.underlined-cell {
    text-decoration: underline;
    text-decoration-color: #00000000;
    text-decoration-thickness: 5px;
    font-weight: bold;
    // color: #0c0c0c;
}
::ng-deep .dx-datagrid .dx-row:hover td:not(:empty) {
    background-color: #a8d4fa; /* Light blue background on hover */
    cursor: pointer; /* Change cursor to pointer */
    transition: background-color 0.3s ease; /* Smooth transition for hover effect */
}
 
::ng-deep .dx-datagrid .dx-row:hover td:empty {
    background-color: transparent; /* No hover effect for empty cells */
    cursor: default; /* Default cursor for empty cells */
}

/* Data Grid */
dx-data-grid {
    width: 100%; // ✅ Makes sure the table fully expands
    border-radius: 8px;
    font-family: 'Poppins', sans-serif;
}

dx-data-grid:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Table Headers */
.dx-datagrid-headers {
    font-weight: bold;
    background: linear-gradient(135deg, #e3f2fd, #bbdefb);
    font-size: 12px;
    text-align: center;
}

/* Responsive Column Adjustments */
dxi-column {
    max-width: 200px; // ✅ Prevents columns from becoming too wide
    white-space: normal; // ✅ Allows text to wrap inside cells
}

/* Ensure Responsive Layout */
@media screen and (max-width: 1200px) {
    dx-data-grid {
        font-size: 11px; // ✅ Adjusts font size for smaller screens
    }
    dxi-column {
        max-width: 150px; // ✅ Makes columns adapt to smaller screens
    }
}

/*  Fixed Column Widths */
.dx-datagrid-headers .dx-row > td,
.dx-datagrid-headers .dx-row > th,
.dx-datagrid-rowsview .dx-row > td {
    border: 1px solid #ddd;
    white-space: nowrap;
}

/*  Equal spacing for all columns except Location and Total Assets */
.dx-datagrid-rowsview .dx-row > td:not(:nth-child(1)):not(:nth-child(2)) {
    text-align: center;
    min-width: 130px;
}

/*  Table Rows */
.dx-row {
    text-align: center;
    font-size: 12px;
}

/*  Table Borders */
.dx-datagrid-headers .dx-row > td,
.dx-datagrid-headers .dx-row > th,
.dx-datagrid-rowsview .dx-row > td {
    border: 1px solid #ddd;
}

.dx-datagrid-rowsview .dx-row {
    border-bottom: 1px solid #ddd;
}

/*  Zebra Striping */
.dx-datagrid-rowsview .dx-row:nth-child(odd) {
    background-color: #f8f9fa;
}

.dx-datagrid-rowsview .dx-row:nth-child(even) {
    background-color: #ffffff;
}

/*  Buttons */
button {
    background: linear-gradient(135deg, #42a5f5, #1e88e5);
    color: white;
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 13px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease-in-out;
}

button:hover {
    background: linear-gradient(135deg, #1e88e5, #1565c0);
    transform: translateY(-2px);
}

.loader-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000; /* Ensure it appears above everything */
}

// .centered {
//     display: flex;
//     justify-content: center;
//     align-items: center;
// }
::ng-deep .custom-gauge .dxg-range {
  transition: filter 0.3s ease;
}

::ng-deep .custom-gauge:hover .dxg-range {
  filter: drop-shadow(0 0 10px rgba(0, 136, 204, 0.8));
}
::ng-deep .custom-gauge .dxg-line {
  transition: transform 0.3s ease;
}

::ng-deep .custom-gauge:hover .dxg-line {
  transform: scale(1.02);
}


.centered {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.location-title {
    font-size: 18px !important;
    font-weight: 700; /* Make it bold */
    text-align: left; /* Center align */
    color: #333; /* Ensure consistent text color */
    margin-bottom: 10px; /* Adjust spacing */
    padding: 10px 0; /* Adjust padding */
    padding-left: 20px;
    display: block;
}

.bold-text {
    font-weight: 700; /* Extra reinforcement for bold text */
}

#districtSelectBox .dx-tagbox-container,
#clientSelectBox .dx-tagbox-container {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.dx-tagbox-multi-tag {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

  