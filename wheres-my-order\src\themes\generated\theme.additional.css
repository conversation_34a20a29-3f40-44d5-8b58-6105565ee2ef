/** Generated by the DevExpress ThemeBuilder
* Version: 22.1.5
* http://js.devexpress.com/ThemeBuilder/
*/

@import url(https://fonts.googleapis.com/css?family=Roboto:300,400,500,700);
@import url(https://fonts.googleapis.com/earlyaccess/notokufiarabic.css);
@font-face {
  font-family: RobotoFallback;
  font-style: normal;
  font-weight: 300;
  src: local("Roboto Light"),local("Roboto-Light"),url("../../../node_modules/devextreme/dist/css/fonts/Roboto-300.woff2") format("woff2"),url("../../../node_modules/devextreme/dist/css/fonts/Roboto-300.woff") format("woff"),url("../../../node_modules/devextreme/dist/css/fonts/Roboto-300.ttf") format("truetype");
}
@font-face {
  font-family: RobotoFallback;
  font-style: normal;
  font-weight: 400;
  src: local("Roboto"),local("Roboto-Regular"),url("../../../node_modules/devextreme/dist/css/fonts/Roboto-400.woff2") format("woff2"),url("../../../node_modules/devextreme/dist/css/fonts/Roboto-400.woff") format("woff"),url("../../../node_modules/devextreme/dist/css/fonts/Roboto-400.ttf") format("truetype");
}
@font-face {
  font-family: RobotoFallback;
  font-style: normal;
  font-weight: 500;
  src: local("Roboto Medium"),local("Roboto-Medium"),url("../../../node_modules/devextreme/dist/css/fonts/Roboto-500.woff2") format("woff2"),url("../../../node_modules/devextreme/dist/css/fonts/Roboto-500.woff") format("woff"),url("../../../node_modules/devextreme/dist/css/fonts/Roboto-500.ttf") format("truetype");
}
@font-face {
  font-family: RobotoFallback;
  font-style: normal;
  font-weight: 700;
  src: local("Roboto Bold"),local("Roboto-Bold"),url("../../../node_modules/devextreme/dist/css/fonts/Roboto-700.woff2") format("woff2"),url("../../../node_modules/devextreme/dist/css/fonts/Roboto-700.woff") format("woff"),url("../../../node_modules/devextreme/dist/css/fonts/Roboto-700.ttf") format("truetype");
}
@font-face {
  font-family: DXIcons;
  src: local("DevExtreme Material Icons"),local("devextreme_material_icons"),url("../../../node_modules/devextreme/dist/css/icons/dxiconsmaterial.woff2") format("woff2"),url("../../../node_modules/devextreme/dist/css/icons/dxiconsmaterial.woff") format("woff"),url("../../../node_modules/devextreme/dist/css/icons/dxiconsmaterial.ttf") format("truetype");
  font-weight: 400;
  font-style: normal;
}
.dx-swatch-additional .dx-validationsummary-item {
  cursor: pointer;
}
.dx-swatch-additional .dx-invalid-message.dx-overlay {
  position: relative;
}
.dx-swatch-additional .dx-invalid-message.dx-overlay-wrapper {
  width: 100%;
  visibility: hidden;
  pointer-events: none;
}
.dx-swatch-additional .dx-invalid-message > .dx-overlay-content {
  display: inline-block;
  border-width: 0;
  font-size: .85em;
  line-height: normal;
  word-wrap: break-word;
}
.dx-swatch-additional .dx-dropdownbox.dx-dropdowneditor-active .dx-invalid-message-auto,
.dx-swatch-additional .dx-invalid-message-visible.dx-invalid .dx-invalid-message-auto,
.dx-swatch-additional .dx-invalid-message.dx-invalid-message-always,
.dx-swatch-additional .dx-lookup.dx-dropdowneditor-active .dx-invalid-message-auto,
.dx-swatch-additional .dx-state-focused.dx-invalid .dx-invalid-message-auto {
  visibility: visible;
}
.dx-swatch-additional .dx-validationsummary-item-content {
  border-bottom: 1px dashed;
  display: inline-block;
  line-height: normal;
}
@-webkit-keyframes dx-valid-badge-frames {
  from {
    opacity: 0;
    -webkit-transform: scale(.1);
    transform: scale(.1);
  }
  to {
    opacity: 1;
    -webkit-transform: scale(1);
    transform: scale(1);
  }
}
@keyframes dx-valid-badge-frames {
  from {
    opacity: 0;
    -webkit-transform: scale(.1);
    transform: scale(.1);
  }
  to {
    opacity: 1;
    -webkit-transform: scale(1);
    transform: scale(1);
  }
}
.dx-theme-material-typography .dx-swatch-additional,
.dx-theme-material-typography.dx-swatch-additional {
  background-color: #363640;
  color: #fff;
  font-weight: 400;
  font-size: 13px;
  font-family: Roboto,RobotoFallback,"Noto Kufi Arabic",Helvetica,Arial,sans-serif;
  line-height: 1.2857;
}
.dx-theme-material-typography .dx-swatch-additional input,
.dx-theme-material-typography .dx-swatch-additional textarea,
.dx-theme-material-typography.dx-swatch-additional input,
.dx-theme-material-typography.dx-swatch-additional textarea {
  font-family: Roboto,RobotoFallback,"Noto Kufi Arabic",Helvetica,Arial,sans-serif;
  line-height: 1.2857;
}
.dx-theme-material-typography .dx-swatch-additional h1,
.dx-theme-material-typography.dx-swatch-additional h1 {
  font-weight: 300;
  font-size: 64px;
  letter-spacing: -1.5px;
}
.dx-theme-material-typography .dx-swatch-additional h2,
.dx-theme-material-typography.dx-swatch-additional h2 {
  font-weight: 300;
  font-size: 42px;
  letter-spacing: -.5px;
}
.dx-theme-material-typography .dx-swatch-additional h3,
.dx-theme-material-typography.dx-swatch-additional h3 {
  font-weight: 400;
  font-size: 20px;
}
.dx-theme-material-typography .dx-swatch-additional h4,
.dx-theme-material-typography.dx-swatch-additional h4 {
  font-weight: 400;
  font-size: 18px;
  letter-spacing: .25px;
}
.dx-theme-material-typography .dx-swatch-additional h5,
.dx-theme-material-typography.dx-swatch-additional h5 {
  font-weight: 400;
  font-size: 16px;
}
.dx-theme-material-typography .dx-swatch-additional h6,
.dx-theme-material-typography.dx-swatch-additional h6 {
  font-weight: 500;
  font-size: 14px;
  letter-spacing: .15px;
}
.dx-theme-material-typography .dx-swatch-additional .dx-font-xl,
.dx-theme-material-typography.dx-swatch-additional .dx-font-xl {
  font-size: 24px;
}
.dx-theme-material-typography .dx-swatch-additional .dx-font-l,
.dx-theme-material-typography.dx-swatch-additional .dx-font-l {
  font-size: 20px;
}
.dx-theme-material-typography .dx-swatch-additional .dx-font-m,
.dx-theme-material-typography.dx-swatch-additional .dx-font-m {
  font-size: 16px;
}
.dx-theme-material-typography .dx-swatch-additional .dx-font-s,
.dx-theme-material-typography.dx-swatch-additional .dx-font-s {
  font-size: 14px;
}
.dx-theme-material-typography .dx-swatch-additional .dx-font-xs,
.dx-theme-material-typography .dx-swatch-additional small,
.dx-theme-material-typography.dx-swatch-additional .dx-font-xs,
.dx-theme-material-typography.dx-swatch-additional small {
  font-size: 12px;
}
.dx-theme-material-typography .dx-swatch-additional a,
.dx-theme-material-typography.dx-swatch-additional a {
  color: #ff5722;
}
.dx-swatch-additional .dx-icon-add,
.dx-swatch-additional .dx-icon-addcolumnleft,
.dx-swatch-additional .dx-icon-addcolumnright,
.dx-swatch-additional .dx-icon-addrowabove,
.dx-swatch-additional .dx-icon-addrowbelow,
.dx-swatch-additional .dx-icon-addtable,
.dx-swatch-additional .dx-icon-airplane,
.dx-swatch-additional .dx-icon-aligncenter,
.dx-swatch-additional .dx-icon-alignjustify,
.dx-swatch-additional .dx-icon-alignleft,
.dx-swatch-additional .dx-icon-alignright,
.dx-swatch-additional .dx-icon-arrowdown,
.dx-swatch-additional .dx-icon-arrowleft,
.dx-swatch-additional .dx-icon-arrowright,
.dx-swatch-additional .dx-icon-arrowup,
.dx-swatch-additional .dx-icon-background,
.dx-swatch-additional .dx-icon-blockquote,
.dx-swatch-additional .dx-icon-bold,
.dx-swatch-additional .dx-icon-bookmark,
.dx-swatch-additional .dx-icon-box,
.dx-swatch-additional .dx-icon-bulletlist,
.dx-swatch-additional .dx-icon-car,
.dx-swatch-additional .dx-icon-card,
.dx-swatch-additional .dx-icon-cart,
.dx-swatch-additional .dx-icon-cellproperties,
.dx-swatch-additional .dx-icon-chart,
.dx-swatch-additional .dx-icon-checklist,
.dx-swatch-additional .dx-icon-clear,
.dx-swatch-additional .dx-icon-clearformat,
.dx-swatch-additional .dx-icon-clock,
.dx-swatch-additional .dx-icon-close,
.dx-swatch-additional .dx-icon-codeblock,
.dx-swatch-additional .dx-icon-color,
.dx-swatch-additional .dx-icon-columnproperties,
.dx-swatch-additional .dx-icon-comment,
.dx-swatch-additional .dx-icon-decreaseindent,
.dx-swatch-additional .dx-icon-doc,
.dx-swatch-additional .dx-icon-download,
.dx-swatch-additional .dx-icon-edit,
.dx-swatch-additional .dx-icon-email,
.dx-swatch-additional .dx-icon-event,
.dx-swatch-additional .dx-icon-favorites,
.dx-swatch-additional .dx-icon-find,
.dx-swatch-additional .dx-icon-folder,
.dx-swatch-additional .dx-icon-fontsize,
.dx-swatch-additional .dx-icon-food,
.dx-swatch-additional .dx-icon-formula,
.dx-swatch-additional .dx-icon-gift,
.dx-swatch-additional .dx-icon-globe,
.dx-swatch-additional .dx-icon-group,
.dx-swatch-additional .dx-icon-growfont,
.dx-swatch-additional .dx-icon-header,
.dx-swatch-additional .dx-icon-help,
.dx-swatch-additional .dx-icon-home,
.dx-swatch-additional .dx-icon-image,
.dx-swatch-additional .dx-icon-increaseindent,
.dx-swatch-additional .dx-icon-indent,
.dx-swatch-additional .dx-icon-info,
.dx-swatch-additional .dx-icon-italic,
.dx-swatch-additional .dx-icon-key,
.dx-swatch-additional .dx-icon-like,
.dx-swatch-additional .dx-icon-link,
.dx-swatch-additional .dx-icon-map,
.dx-swatch-additional .dx-icon-mention,
.dx-swatch-additional .dx-icon-menu,
.dx-swatch-additional .dx-icon-mergecells,
.dx-swatch-additional .dx-icon-money,
.dx-swatch-additional .dx-icon-music,
.dx-swatch-additional .dx-icon-orderedlist,
.dx-swatch-additional .dx-icon-overflow,
.dx-swatch-additional .dx-icon-percent,
.dx-swatch-additional .dx-icon-photo,
.dx-swatch-additional .dx-icon-plus,
.dx-swatch-additional .dx-icon-preferences,
.dx-swatch-additional .dx-icon-product,
.dx-swatch-additional .dx-icon-redo,
.dx-swatch-additional .dx-icon-refresh,
.dx-swatch-additional .dx-icon-remove,
.dx-swatch-additional .dx-icon-removecolumn,
.dx-swatch-additional .dx-icon-removerow,
.dx-swatch-additional .dx-icon-removetable,
.dx-swatch-additional .dx-icon-rowproperties,
.dx-swatch-additional .dx-icon-runner,
.dx-swatch-additional .dx-icon-save,
.dx-swatch-additional .dx-icon-search,
.dx-swatch-additional .dx-icon-shrinkfont,
.dx-swatch-additional .dx-icon-splitcells,
.dx-swatch-additional .dx-icon-strike,
.dx-swatch-additional .dx-icon-subscript,
.dx-swatch-additional .dx-icon-superscript,
.dx-swatch-additional .dx-icon-tableproperties,
.dx-swatch-additional .dx-icon-tags,
.dx-swatch-additional .dx-icon-tel,
.dx-swatch-additional .dx-icon-tips,
.dx-swatch-additional .dx-icon-todo,
.dx-swatch-additional .dx-icon-toolbox,
.dx-swatch-additional .dx-icon-underline,
.dx-swatch-additional .dx-icon-undo,
.dx-swatch-additional .dx-icon-user,
.dx-swatch-additional .dx-icon-variable,
.dx-swatch-additional .dx-icon-verticalalignbottom,
.dx-swatch-additional .dx-icon-verticalaligncenter,
.dx-swatch-additional .dx-icon-verticalaligntop,
.dx-swatch-additional .dx-icon-video {
  background-position: 0 0;
  background-repeat: no-repeat;
}
.dx-swatch-additional .dx-icon {
  background-position: 50% 50%;
  display: inline-block;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.dx-swatch-additional .dx-svg-icon svg {
  pointer-events: none;
}
.dx-swatch-additional .dx-rtl .dx-icon-spinnext::before {
  content: "\f04f";
}
.dx-swatch-additional .dx-rtl .dx-icon-spinprev::before {
  content: "\f04e";
}
.dx-swatch-additional .dx-rtl .dx-icon-chevronnext::before {
  content: "\f012";
}
.dx-swatch-additional .dx-rtl .dx-icon-back::before,
.dx-swatch-additional .dx-rtl .dx-icon-chevronprev::before {
  content: "\f010";
}
.dx-swatch-additional .dx-rtl .dx-icon-undo::before {
  content: "\f093";
}
.dx-swatch-additional .dx-rtl .dx-icon-redo::before {
  content: "\f04c";
}
.dx-swatch-additional .dx-rtl .dx-icon-hidepanel::before {
  content: "\f11d";
}
.dx-swatch-additional .dx-rtl .dx-icon-showpanel::before {
  content: "\f11c";
}
.dx-swatch-additional .dx-icon-add {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-add::before {
  content: "\f00b";
}
.dx-swatch-additional .dx-icon-airplane {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-airplane::before {
  content: "\f000";
}
.dx-swatch-additional .dx-icon-bookmark {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-bookmark::before {
  content: "\f017";
}
.dx-swatch-additional .dx-icon-box {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-box::before {
  content: "\f018";
}
.dx-swatch-additional .dx-icon-car {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-car::before {
  content: "\f01b";
}
.dx-swatch-additional .dx-icon-card {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-card::before {
  content: "\f019";
}
.dx-swatch-additional .dx-icon-cart {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-cart::before {
  content: "\f01a";
}
.dx-swatch-additional .dx-icon-chart {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-chart::before {
  content: "\f01c";
}
.dx-swatch-additional .dx-icon-check {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-check::before {
  content: "\f005";
}
.dx-swatch-additional .dx-icon-clear {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-clear::before {
  content: "\f008";
}
.dx-swatch-additional .dx-icon-clock {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-clock::before {
  content: "\f01d";
}
.dx-swatch-additional .dx-icon-close {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-close::before {
  content: "\f00a";
}
.dx-swatch-additional .dx-icon-coffee {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-coffee::before {
  content: "\f02a";
}
.dx-swatch-additional .dx-icon-comment {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-comment::before {
  content: "\f01e";
}
.dx-swatch-additional .dx-icon-doc {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-doc::before {
  content: "\f021";
}
.dx-swatch-additional .dx-icon-file {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-file::before {
  content: "\f021";
}
.dx-swatch-additional .dx-icon-download {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-download::before {
  content: "\f022";
}
.dx-swatch-additional .dx-icon-dragvertical {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-dragvertical::before {
  content: "\f038";
}
.dx-swatch-additional .dx-icon-edit {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-edit::before {
  content: "\f023";
}
.dx-swatch-additional .dx-icon-email {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-email::before {
  content: "\f024";
}
.dx-swatch-additional .dx-icon-event {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-event::before {
  content: "\f026";
}
.dx-swatch-additional .dx-icon-favorites {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-favorites::before {
  content: "\f025";
}
.dx-swatch-additional .dx-icon-find {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-find::before {
  content: "\f027";
}
.dx-swatch-additional .dx-icon-filter {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-filter::before {
  content: "\f050";
}
.dx-swatch-additional .dx-icon-folder {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-folder::before {
  content: "\f028";
}
.dx-swatch-additional .dx-icon-activefolder {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-activefolder::before {
  content: "\f028";
}
.dx-swatch-additional .dx-icon-food {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-food::before {
  content: "\f029";
}
.dx-swatch-additional .dx-icon-gift {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-gift::before {
  content: "\f02b";
}
.dx-swatch-additional .dx-icon-globe {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-globe::before {
  content: "\f02c";
}
.dx-swatch-additional .dx-icon-group {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-group::before {
  content: "\f02e";
}
.dx-swatch-additional .dx-icon-help {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-help::before {
  content: "\f02f";
}
.dx-swatch-additional .dx-icon-home {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-home::before {
  content: "\f030";
}
.dx-swatch-additional .dx-icon-image {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-image::before {
  content: "\f031";
}
.dx-swatch-additional .dx-icon-info {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-info::before {
  content: "\f032";
}
.dx-swatch-additional .dx-icon-key {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-key::before {
  content: "\f033";
}
.dx-swatch-additional .dx-icon-like {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-like::before {
  content: "\f034";
}
.dx-swatch-additional .dx-icon-map {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-map::before {
  content: "\f035";
}
.dx-swatch-additional .dx-icon-menu {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-menu::before {
  content: "\f00c";
}
.dx-swatch-additional .dx-icon-message {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-message::before {
  content: "\f024";
}
.dx-swatch-additional .dx-icon-money {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-money::before {
  content: "\f036";
}
.dx-swatch-additional .dx-icon-music {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-music::before {
  content: "\f037";
}
.dx-swatch-additional .dx-icon-overflow {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-overflow::before {
  content: "\f00d";
}
.dx-swatch-additional .dx-icon-percent {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-percent::before {
  content: "\f039";
}
.dx-swatch-additional .dx-icon-photo {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-photo::before {
  content: "\f03a";
}
.dx-swatch-additional .dx-icon-plus {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-plus::before {
  content: "\f00b";
}
.dx-swatch-additional .dx-icon-minus {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-minus::before {
  content: "\f074";
}
.dx-swatch-additional .dx-icon-preferences {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-preferences::before {
  content: "\f03b";
}
.dx-swatch-additional .dx-icon-product {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-product::before {
  content: "\f03c";
}
.dx-swatch-additional .dx-icon-pulldown {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-pulldown::before {
  content: "\f062";
}
.dx-swatch-additional .dx-icon-refresh {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-refresh::before {
  content: "\f03d";
}
.dx-swatch-additional .dx-icon-remove {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-remove::before {
  content: "\f00a";
}
.dx-swatch-additional .dx-icon-revert {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-revert::before {
  content: "\f04c";
}
.dx-swatch-additional .dx-icon-runner {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-runner::before {
  content: "\f040";
}
.dx-swatch-additional .dx-icon-save {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-save::before {
  content: "\f041";
}
.dx-swatch-additional .dx-icon-search {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-search::before {
  content: "\f027";
}
.dx-swatch-additional .dx-icon-tags {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-tags::before {
  content: "\f009";
}
.dx-swatch-additional .dx-icon-tel {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-tel::before {
  content: "\f003";
}
.dx-swatch-additional .dx-icon-tips {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-tips::before {
  content: "\f004";
}
.dx-swatch-additional .dx-icon-todo {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-todo::before {
  content: "\f005";
}
.dx-swatch-additional .dx-icon-toolbox {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-toolbox::before {
  content: "\f007";
}
.dx-swatch-additional .dx-icon-trash {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-trash::before {
  content: "\f03e";
}
.dx-swatch-additional .dx-icon-user {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-user::before {
  content: "\f02d";
}
.dx-swatch-additional .dx-icon-upload {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-upload::before {
  content: "\f006";
}
.dx-swatch-additional .dx-icon-floppy {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-floppy::before {
  content: "\f073";
}
.dx-swatch-additional .dx-icon-arrowleft {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-arrowleft::before {
  content: "\f011";
}
.dx-swatch-additional .dx-icon-arrowdown {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-arrowdown::before {
  content: "\f015";
}
.dx-swatch-additional .dx-icon-arrowright {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-arrowright::before {
  content: "\f00e";
}
.dx-swatch-additional .dx-icon-arrowup {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-arrowup::before {
  content: "\f013";
}
.dx-swatch-additional .dx-icon-spinleft {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-spinleft::before {
  content: "\f04f";
}
.dx-swatch-additional .dx-icon-spinprev {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-spinprev::before {
  content: "\f04f";
}
.dx-swatch-additional .dx-icon-spinright {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-spinright::before {
  content: "\f04e";
}
.dx-swatch-additional .dx-icon-spinnext {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-spinnext::before {
  content: "\f04e";
}
.dx-swatch-additional .dx-icon-spindown {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-spindown::before {
  content: "\f001";
}
.dx-swatch-additional .dx-icon-spinup {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-spinup::before {
  content: "\f002";
}
.dx-swatch-additional .dx-icon-chevronleft {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-chevronleft::before {
  content: "\f012";
}
.dx-swatch-additional .dx-icon-chevronprev {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-chevronprev::before {
  content: "\f012";
}
.dx-swatch-additional .dx-icon-back {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-back::before {
  content: "\f012";
}
.dx-swatch-additional .dx-icon-chevronright {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-chevronright::before {
  content: "\f010";
}
.dx-swatch-additional .dx-icon-chevronnext {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-chevronnext::before {
  content: "\f010";
}
.dx-swatch-additional .dx-icon-chevrondown {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-chevrondown::before {
  content: "\f016";
}
.dx-swatch-additional .dx-icon-chevronup {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-chevronup::before {
  content: "\f014";
}
.dx-swatch-additional .dx-icon-chevrondoubleleft {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-chevrondoubleleft::before {
  content: "\f042";
}
.dx-swatch-additional .dx-icon-chevrondoubleright {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-chevrondoubleright::before {
  content: "\f03f";
}
.dx-swatch-additional .dx-icon-equal {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-equal::before {
  content: "\f044";
}
.dx-swatch-additional .dx-icon-notequal {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-notequal::before {
  content: "\f045";
}
.dx-swatch-additional .dx-icon-less {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-less::before {
  content: "\f046";
}
.dx-swatch-additional .dx-icon-greater {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-greater::before {
  content: "\f047";
}
.dx-swatch-additional .dx-icon-lessorequal {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-lessorequal::before {
  content: "\f048";
}
.dx-swatch-additional .dx-icon-greaterorequal {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-greaterorequal::before {
  content: "\f049";
}
.dx-swatch-additional .dx-icon-isblank {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-isblank::before {
  content: "\f075";
}
.dx-swatch-additional .dx-icon-isnotblank {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-isnotblank::before {
  content: "\f076";
}
.dx-swatch-additional .dx-icon-sortup {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-sortup::before {
  content: "\f051";
}
.dx-swatch-additional .dx-icon-sortdown {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-sortdown::before {
  content: "\f052";
}
.dx-swatch-additional .dx-icon-sortuptext {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-sortuptext::before {
  content: "\f053";
}
.dx-swatch-additional .dx-icon-sortdowntext {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-sortdowntext::before {
  content: "\f054";
}
.dx-swatch-additional .dx-icon-sorted {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-sorted::before {
  content: "\f055";
}
.dx-swatch-additional .dx-icon-expand {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-expand::before {
  content: "\f04a";
}
.dx-swatch-additional .dx-icon-collapse {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-collapse::before {
  content: "\f04b";
}
.dx-swatch-additional .dx-icon-columnfield {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-columnfield::before {
  content: "\f057";
}
.dx-swatch-additional .dx-icon-rowfield {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-rowfield::before {
  content: "\f058";
}
.dx-swatch-additional .dx-icon-datafield {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-datafield::before {
  content: "\f101";
}
.dx-swatch-additional .dx-icon-fields {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-fields::before {
  content: "\f059";
}
.dx-swatch-additional .dx-icon-fieldchooser {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-fieldchooser::before {
  content: "\f05a";
}
.dx-swatch-additional .dx-icon-columnchooser {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-columnchooser::before {
  content: "\f04d";
}
.dx-swatch-additional .dx-icon-pin {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-pin::before {
  content: "\f05b";
}
.dx-swatch-additional .dx-icon-unpin {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-unpin::before {
  content: "\f05c";
}
.dx-swatch-additional .dx-icon-pinleft {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-pinleft::before {
  content: "\f05d";
}
.dx-swatch-additional .dx-icon-pinright {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-pinright::before {
  content: "\f05e";
}
.dx-swatch-additional .dx-icon-contains {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-contains::before {
  content: "\f063";
}
.dx-swatch-additional .dx-icon-startswith {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-startswith::before {
  content: "\f064";
}
.dx-swatch-additional .dx-icon-endswith {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-endswith::before {
  content: "\f065";
}
.dx-swatch-additional .dx-icon-doesnotcontain {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-doesnotcontain::before {
  content: "\f066";
}
.dx-swatch-additional .dx-icon-range {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-range::before {
  content: "\f06a";
}
.dx-swatch-additional .dx-icon-export {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-export::before {
  content: "\f05f";
}
.dx-swatch-additional .dx-icon-exportxlsx {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-exportxlsx::before {
  content: "\f060";
}
.dx-swatch-additional .dx-icon-exportpdf {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-exportpdf::before {
  content: "\f061";
}
.dx-swatch-additional .dx-icon-exportselected {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-exportselected::before {
  content: "\f06d";
}
.dx-swatch-additional .dx-icon-warning {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-warning::before {
  content: "\f06b";
}
.dx-swatch-additional .dx-icon-more {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-more::before {
  content: "\f06c";
}
.dx-swatch-additional .dx-icon-square {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-square::before {
  content: "\f067";
}
.dx-swatch-additional .dx-icon-clearsquare {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-clearsquare::before {
  content: "\f068";
}
.dx-swatch-additional .dx-icon-repeat {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-repeat::before {
  content: "\f069";
}
.dx-swatch-additional .dx-icon-selectall {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-selectall::before {
  content: "\f070";
}
.dx-swatch-additional .dx-icon-unselectall {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-unselectall::before {
  content: "\f071";
}
.dx-swatch-additional .dx-icon-print {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-print::before {
  content: "\f072";
}
.dx-swatch-additional .dx-icon-bold {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-bold::before {
  content: "\f077";
}
.dx-swatch-additional .dx-icon-italic {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-italic::before {
  content: "\f078";
}
.dx-swatch-additional .dx-icon-underline {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-underline::before {
  content: "\f079";
}
.dx-swatch-additional .dx-icon-strike {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-strike::before {
  content: "\f07a";
}
.dx-swatch-additional .dx-icon-indent {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-indent::before {
  content: "\f07b";
}
.dx-swatch-additional .dx-icon-increaselinespacing {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-increaselinespacing::before {
  content: "\f07b";
}
.dx-swatch-additional .dx-icon-font {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-font::before {
  content: "\f11b";
}
.dx-swatch-additional .dx-icon-fontsize {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-fontsize::before {
  content: "\f07c";
}
.dx-swatch-additional .dx-icon-shrinkfont {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-shrinkfont::before {
  content: "\f07d";
}
.dx-swatch-additional .dx-icon-growfont {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-growfont::before {
  content: "\f07e";
}
.dx-swatch-additional .dx-icon-color {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-color::before {
  content: "\f07f";
}
.dx-swatch-additional .dx-icon-background {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-background::before {
  content: "\f080";
}
.dx-swatch-additional .dx-icon-fill {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-fill::before {
  content: "\f10d";
}
.dx-swatch-additional .dx-icon-palette {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-palette::before {
  content: "\f120";
}
.dx-swatch-additional .dx-icon-superscript {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-superscript::before {
  content: "\f081";
}
.dx-swatch-additional .dx-icon-subscript {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-subscript::before {
  content: "\f082";
}
.dx-swatch-additional .dx-icon-header {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-header::before {
  content: "\f083";
}
.dx-swatch-additional .dx-icon-blockquote {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-blockquote::before {
  content: "\f084";
}
.dx-swatch-additional .dx-icon-formula {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-formula::before {
  content: "\f056";
}
.dx-swatch-additional .dx-icon-codeblock {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-codeblock::before {
  content: "\f085";
}
.dx-swatch-additional .dx-icon-orderedlist {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-orderedlist::before {
  content: "\f086";
}
.dx-swatch-additional .dx-icon-bulletlist {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-bulletlist::before {
  content: "\f087";
}
.dx-swatch-additional .dx-icon-increaseindent {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-increaseindent::before {
  content: "\f088";
}
.dx-swatch-additional .dx-icon-decreaseindent {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-decreaseindent::before {
  content: "\f089";
}
.dx-swatch-additional .dx-icon-decreaselinespacing {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-decreaselinespacing::before {
  content: "\f106";
}
.dx-swatch-additional .dx-icon-alignleft {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-alignleft::before {
  content: "\f08a";
}
.dx-swatch-additional .dx-icon-alignright {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-alignright::before {
  content: "\f08b";
}
.dx-swatch-additional .dx-icon-aligncenter {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-aligncenter::before {
  content: "\f08c";
}
.dx-swatch-additional .dx-icon-alignjustify {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-alignjustify::before {
  content: "\f08d";
}
.dx-swatch-additional .dx-icon-link {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-link::before {
  content: "\f08e";
}
.dx-swatch-additional .dx-icon-video {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-video::before {
  content: "\f08f";
}
.dx-swatch-additional .dx-icon-mention {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-mention::before {
  content: "\f090";
}
.dx-swatch-additional .dx-icon-variable {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-variable::before {
  content: "\f091";
}
.dx-swatch-additional .dx-icon-clearformat {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-clearformat::before {
  content: "\f092";
}
.dx-swatch-additional .dx-icon-fullscreen {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-fullscreen::before {
  content: "\f11a";
}
.dx-swatch-additional .dx-icon-hierarchy {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-hierarchy::before {
  content: "\f124";
}
.dx-swatch-additional .dx-icon-docfile {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-docfile::before {
  content: "\f111";
}
.dx-swatch-additional .dx-icon-docxfile {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-docxfile::before {
  content: "\f110";
}
.dx-swatch-additional .dx-icon-pdffile {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-pdffile::before {
  content: "\f118";
}
.dx-swatch-additional .dx-icon-pptfile {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-pptfile::before {
  content: "\f114";
}
.dx-swatch-additional .dx-icon-pptxfile {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-pptxfile::before {
  content: "\f115";
}
.dx-swatch-additional .dx-icon-rtffile {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-rtffile::before {
  content: "\f112";
}
.dx-swatch-additional .dx-icon-txtfile {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-txtfile::before {
  content: "\f113";
}
.dx-swatch-additional .dx-icon-xlsfile {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-xlsfile::before {
  content: "\f116";
}
.dx-swatch-additional .dx-icon-xlsxfile {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-xlsxfile::before {
  content: "\f117";
}
.dx-swatch-additional .dx-icon-copy {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-copy::before {
  content: "\f107";
}
.dx-swatch-additional .dx-icon-cut {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-cut::before {
  content: "\f10a";
}
.dx-swatch-additional .dx-icon-paste {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-paste::before {
  content: "\f108";
}
.dx-swatch-additional .dx-icon-share {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-share::before {
  content: "\f11f";
}
.dx-swatch-additional .dx-icon-inactivefolder {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-inactivefolder::before {
  content: "\f105";
}
.dx-swatch-additional .dx-icon-newfolder {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-newfolder::before {
  content: "\f123";
}
.dx-swatch-additional .dx-icon-movetofolder {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-movetofolder::before {
  content: "\f121";
}
.dx-swatch-additional .dx-icon-parentfolder {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-parentfolder::before {
  content: "\f122";
}
.dx-swatch-additional .dx-icon-rename {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-rename::before {
  content: "\f109";
}
.dx-swatch-additional .dx-icon-detailslayout {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-detailslayout::before {
  content: "\f10b";
}
.dx-swatch-additional .dx-icon-contentlayout {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-contentlayout::before {
  content: "\f11e";
}
.dx-swatch-additional .dx-icon-smalliconslayout {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-smalliconslayout::before {
  content: "\f119";
}
.dx-swatch-additional .dx-icon-mediumiconslayout {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-mediumiconslayout::before {
  content: "\f10c";
}
.dx-swatch-additional .dx-icon-undo {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-undo::before {
  content: "\f04c";
}
.dx-swatch-additional .dx-icon-redo {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-redo::before {
  content: "\f093";
}
.dx-swatch-additional .dx-icon-hidepanel {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-hidepanel::before {
  content: "\f11c";
}
.dx-swatch-additional .dx-icon-showpanel {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-showpanel::before {
  content: "\f11d";
}
.dx-swatch-additional .dx-icon-checklist {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-checklist::before {
  content: "\f141";
}
.dx-swatch-additional .dx-icon-verticalaligntop {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-verticalaligntop::before {
  content: "\f14f";
}
.dx-swatch-additional .dx-icon-verticalaligncenter {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-verticalaligncenter::before {
  content: "\f14e";
}
.dx-swatch-additional .dx-icon-verticalalignbottom {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-verticalalignbottom::before {
  content: "\f14d";
}
.dx-swatch-additional .dx-icon-rowproperties {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-rowproperties::before {
  content: "\f14c";
}
.dx-swatch-additional .dx-icon-columnproperties {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-columnproperties::before {
  content: "\f14b";
}
.dx-swatch-additional .dx-icon-cellproperties {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-cellproperties::before {
  content: "\f14a";
}
.dx-swatch-additional .dx-icon-tableproperties {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-tableproperties::before {
  content: "\f140";
}
.dx-swatch-additional .dx-icon-splitcells {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-splitcells::before {
  content: "\f139";
}
.dx-swatch-additional .dx-icon-mergecells {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-mergecells::before {
  content: "\f138";
}
.dx-swatch-additional .dx-icon-deleterow {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-deleterow::before {
  content: "\f137";
}
.dx-swatch-additional .dx-icon-deletecolumn {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-deletecolumn::before {
  content: "\f136";
}
.dx-swatch-additional .dx-icon-insertrowabove {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-insertrowabove::before {
  content: "\f135";
}
.dx-swatch-additional .dx-icon-insertrowbelow {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-insertrowbelow::before {
  content: "\f134";
}
.dx-swatch-additional .dx-icon-insertcolumnleft {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-insertcolumnleft::before {
  content: "\f133";
}
.dx-swatch-additional .dx-icon-insertcolumnright {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-insertcolumnright::before {
  content: "\f132";
}
.dx-swatch-additional .dx-icon-inserttable {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-inserttable::before {
  content: "\f130";
}
.dx-swatch-additional .dx-icon-deletetable {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-deletetable::before {
  content: "\f131";
}
.dx-swatch-additional .dx-icon-edittableheader {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-edittableheader::before {
  content: "\f142";
}
.dx-swatch-additional .dx-icon-addtableheader {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-addtableheader::before {
  content: "\f143";
}
.dx-swatch-additional .dx-icon-pasteplaintext {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-pasteplaintext::before {
  content: "\f144";
}
.dx-swatch-additional .dx-icon-importselected {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-importselected::before {
  content: "\f145";
}
.dx-swatch-additional .dx-icon-import {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-import::before {
  content: "\f146";
}
.dx-swatch-additional .dx-icon-textdocument {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-textdocument::before {
  content: "\f147";
}
.dx-swatch-additional .dx-icon-jpgfile {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-jpgfile::before {
  content: "\f148";
}
.dx-swatch-additional .dx-icon-bmpfile {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-bmpfile::before {
  content: "\f149";
}
.dx-swatch-additional .dx-icon-svgfile {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-svgfile::before {
  content: "\f150";
}
.dx-swatch-additional .dx-icon-attach {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-attach::before {
  content: "\f151";
}
.dx-swatch-additional .dx-icon-return {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-return::before {
  content: "\f152";
}
.dx-swatch-additional .dx-icon-indeterminatestate {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-indeterminatestate::before {
  content: "\f153";
}
.dx-swatch-additional .dx-icon-lock {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-lock::before {
  content: "\f154";
}
.dx-swatch-additional .dx-icon-unlock {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-unlock::before {
  content: "\f155";
}
.dx-swatch-additional .dx-icon-imgarlock {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-imgarlock::before {
  content: "\f156";
}
.dx-swatch-additional .dx-icon-imgarunlock {
  font: 14px/1 DXIcons;
}
.dx-swatch-additional .dx-icon-imgarunlock::before {
  content: "\f157";
}
.dx-swatch-additional .dx-icon {
  font-size: 24px;
  line-height: 1;
  white-space: nowrap;
  text-rendering: optimizeLegibility;
  -webkit-font-feature-settings: "liga";
  font-feature-settings: "liga";
}
.dx-swatch-additional .dx-tab .dx-icon,
.dx-swatch-additional .dx-tab.dx-tab-selected .dx-icon {
  background-size: 100% 100%;
  background-position: 50% 50%;
}
.dx-swatch-additional .dx-scrollview-pulldown {
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAABkCAQAAABebbrxAAABD0lEQVRo3u2XvQ3CMBCFLbmjYYGsAA2wA1X2gAbEAEwB2eIKflagh6zACJAuUihASUic+M5GNH56dT7J8efTPUXKkDkzrS8LpQAEMBygcwAss2UGQADDBmLa+AMvzAAIYNhATBt/YMEMgACGDcS0wbQBEEAAAQQQwD8CEzaiL7sKqOnojTuQrh95SKkX7kqD5j+M6O6Mu1NkupQJZU64B426bjmmXIzLKe7TZiUGLmweyhTa28XWdJKpYn8pXIVub1U4T4+jUKkKbyWeWhR6Vqpwd+w+hb5U4S/ta54qkhZgVihxrxWaznZVZD2lqVDaVkVafOoKGVWRN6nZR6GMxr+qZjHl3aq4db0NLXld7wVjuu7NS9f7yAAAAABJRU5ErkJggg==");
  background-position: 0 0;
  background-repeat: no-repeat;
}
.dx-swatch-additional .dx-loadindicator-image-small {
  background-image: url("data:image/gif;base64,R0lGODlhFAAUAKECADI6RTI6Rv///////yH/C05FVFNDQVBFMi4wAwEAAAAh+QQJCQABACwAAAAAFAAUAAACI4yPqZsADM+LcNJlb9Mq8+B8iCeWBqmFJnqpJUu5ojzDplIAACH5BAkJAAEALAAAAAAUABQAAAIhjI+py+3gXmxwrmoRzgZ4fnxgIIIl523o2KmZ+7KdTIMFACH5BAkJAAIALAAAAAAUABQAAAIflI+py+0Po4zAgDptFhXP60ngNmYdyaGBiYXbC8dwAQAh+QQJCQADACwAAAAAFAAUAAACIpyPqcsL3cCDSlJ368xnc+Nx1geG2Uiin3mpIlnC7gnXTAEAIfkECQkAAwAsAAAAABQAFAAAAiKcD6e74AxRivHRenGGc6vuIWEzluaJbuC4eq36XlboxGUBACH5BAkJAAMALAAAAAAUABQAAAIjnA8Jx226nBxp2mpnzG7z5n3iSJbmiaaqFIrt93LYOMP1UQAAIfkECQkAAwAsAAAAABQAFAAAAh2cD6l53eyiA7Iii7PevPsPhuJIluZpUB6ELWxTAAAh+QQJCQADACwAAAAAFAAUAAACHZx/oMit/5p0a9oBrt68+w+G4kiW5rllYbRCLFIAACH5BAkJAAMALAAAAAAUABQAAAIenH+ggO24noRq2molzo3xD4biSJbmSXqpuYlR2ToFACH5BAkJAAMALAAAAAAUABQAAAIhnI+pi+AMzYsQ0HrXzI2n7Q1WSJbMSKIh6Kmty7GtKWUFACH5BAkJAAMALAAAAAAUABQAAAIinI+py+3gXmxwKlAtytpgrmHdIY5DOX6mt56t24Kd/NZMAQAh+QQJCQADACwAAAAAFAAUAAACIZyPqcvtD6OMwIA6w8Czcnl91DVZW3mKkIeqK+ai8kyXBQAh+QQJCQADACwAAAAAFAAUAAACI5yPqcsL3cCDSlJ368xn82F9RiiSn8l5pziqmXuhMUzR7F0AACH5BAkJAAMALAAAAAAUABQAAAIfnI+pB70/HFxyKmBp1rv7D4aMiIXld6KmmW6V+7pKAQAh+QQJCQADACwAAAAAFAAUAAACIZw/oMi9Dc2LEVBqL8y6+w+G4kiWJBein+pNK4sp8CY3BQAh+QQJCQADACwAAAAAFAAUAAACHZw/oIt96iICstqLs968+w+G4kh+VHdukLW06VEAACH5BAkJAAMALAAAAAAUABQAAAIbnI+pCu29InKygoqz3rz7D4biSJbZ9VHpoyIFACH5BAkJAAMALAAAAAAUABQAAAIfnI8AyM26nDxq2hGvy7r7D4biSJYg51WiGkKju8JOAQA7");
  background-position: center center;
  background-repeat: no-repeat;
}
.dx-swatch-additional .dx-loadindicator-image-large {
  background-image: url("data:image/gif;base64,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");
  background-position: center center;
  background-repeat: no-repeat;
}
.dx-swatch-additional .dx-color-scheme {
  font-family: "#";
}
.dx-swatch-additional .dx-widget {
  display: block;
  -ms-content-zooming: none;
  -webkit-tap-highlight-color: transparent;
  -webkit-text-size-adjust: none;
  -webkit-touch-callout: none;
  padding: 0;
  outline: 0;
  -webkit-print-color-adjust: exact;
  print-color-adjust: exact;
  color: #fff;
  font-weight: 400;
  font-size: 13px;
  font-family: Roboto,RobotoFallback,"Noto Kufi Arabic",Helvetica,Arial,sans-serif;
  line-height: 1.2857;
}
.dx-swatch-additional .dx-widget,
.dx-swatch-additional .dx-widget *,
.dx-swatch-additional .dx-widget ::after,
.dx-swatch-additional .dx-widget ::before,
.dx-swatch-additional .dx-widget::after,
.dx-swatch-additional .dx-widget::before {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
.dx-swatch-additional .dx-item {
  outline: 0;
}
.dx-swatch-additional .dx-rtl {
  direction: rtl;
  unicode-bidi: embed;
}
.dx-swatch-additional .dx-state-disabled {
  pointer-events: none;
}
.dx-swatch-additional .dx-widget input,
.dx-swatch-additional .dx-widget textarea {
  font-family: Roboto,RobotoFallback,"Noto Kufi Arabic",Helvetica,Arial,sans-serif;
  line-height: 1.2857;
}
.dx-swatch-additional .dx-state-disabled .dx-widget,
.dx-swatch-additional .dx-state-disabled.dx-widget {
  opacity: .38;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  cursor: default;
}
.dx-swatch-additional .dx-state-disabled .dx-widget .dx-widget,
.dx-swatch-additional .dx-state-disabled.dx-widget .dx-widget {
  opacity: 1;
}
.dx-swatch-additional .dx-card {
  overflow: hidden;
  -webkit-box-shadow: 0 1px 3px rgba(0,0,0,.12),0 1px 2px rgba(0,0,0,.24);
  box-shadow: 0 1px 3px rgba(0,0,0,.12),0 1px 2px rgba(0,0,0,.24);
  border-radius: 2px;
  background-color: #363640;
  margin: 2px 2px 3px;
}
.dx-swatch-additional .dx-fieldset .dx-field-value {
  margin: 0;
}
.dx-swatch-additional .dx-fieldset,
.dx-swatch-additional .dx-fieldset * {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
.dx-swatch-additional .dx-fieldset-header:empty {
  display: none;
}
.dx-swatch-additional .dx-field {
  position: relative;
  color: #fff;
  font-weight: 400;
  font-size: 13px;
  font-family: Roboto,RobotoFallback,"Noto Kufi Arabic",Helvetica,Arial,sans-serif;
  line-height: 1.2857;
  padding: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}
.dx-swatch-additional .dx-field::after,
.dx-swatch-additional .dx-field::before {
  display: table;
  content: "";
  line-height: 0;
}
.dx-swatch-additional .dx-field::after {
  clear: both;
}
.dx-swatch-additional .dx-field-label {
  float: left;
  width: 40%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.dx-swatch-additional .dx-field-value,
.dx-swatch-additional .dx-field-value-static {
  float: right;
}
.dx-swatch-additional .dx-field-value.dx-datebox {
  min-width: 60%;
}
.dx-swatch-additional .dx-field-value:not(.dx-widget) > .dx-datebox {
  min-width: 100%;
}
.dx-swatch-additional .dx-field-value .dx-selectbox-tag-container {
  white-space: normal;
}
.dx-swatch-additional .dx-field-value:not(.dx-widget) > .dx-selectbox.dx-selectbox-multiselect.dx-widget {
  position: relative;
  width: auto;
  text-align: left;
}
.dx-swatch-additional .dx-fieldset.dx-rtl .dx-field-label,
.dx-swatch-additional .dx-rtl .dx-fieldset .dx-field-label {
  float: right;
}
.dx-swatch-additional .dx-fieldset.dx-rtl .dx-field-value,
.dx-swatch-additional .dx-rtl .dx-fieldset .dx-field-value {
  float: left;
}
.dx-swatch-additional .dx-field input,
.dx-swatch-additional .dx-field textarea {
  font-family: Roboto,RobotoFallback,"Noto Kufi Arabic",Helvetica,Arial,sans-serif;
  line-height: 1.2857;
}
.dx-swatch-additional .dx-field-label i {
  font-style: normal;
}
.dx-swatch-additional .dx-field-value.dx-attention {
  color: #f44336;
  padding: 21px 8px 20px 24px;
  position: relative;
}
.dx-swatch-additional .dx-field-value.dx-attention::before {
  pointer-events: none;
  position: absolute;
  top: 50%;
  margin-top: -8px;
  width: 16px;
  height: 16px;
  text-align: center;
  line-height: 17px;
  font-size: 13px;
  font-weight: 500;
  background-color: #f44336;
  color: rgba(0,0,0,.87);
  content: "!";
  border-radius: 50%;
  left: 0;
}
.dx-swatch-additional .dx-field-value-static,
.dx-swatch-additional .dx-field-value:not(.dx-switch):not(.dx-checkbox):not(.dx-button) {
  width: 60%;
}
.dx-swatch-additional .dx-field-label {
  color: #fff;
  font-size: 12px;
  cursor: default;
  -ms-flex-item-align: center;
  align-self: center;
  padding-right: 12px;
}
.dx-swatch-additional .dx-field-value.dx-widget,
.dx-swatch-additional .dx-field-value:not(.dx-widget) > .dx-widget {
  margin: 0;
}
.dx-swatch-additional .dx-field-value:not(.dx-widget) > .dx-button,
.dx-swatch-additional .dx-field-value:not(.dx-widget) > .dx-checkbox,
.dx-swatch-additional .dx-field-value:not(.dx-widget) > .dx-switch {
  float: right;
}
.dx-swatch-additional .dx-field-value.dx-checkbox,
.dx-swatch-additional .dx-field-value:not(.dx-widget) > .dx-checkbox {
  margin: 8px 0;
}
.dx-swatch-additional .dx-field-value.dx-switch,
.dx-swatch-additional .dx-field-value:not(.dx-widget) > .dx-switch {
  margin: 7px 0;
}
.dx-swatch-additional .dx-field-value.dx-slider,
.dx-swatch-additional .dx-field-value:not(.dx-widget) > .dx-slider {
  margin: 2px 0;
}
.dx-swatch-additional .dx-field-value.dx-radiogroup,
.dx-swatch-additional .dx-field-value:not(.dx-widget) > .dx-radiogroup {
  margin: 3px 0;
}
.dx-swatch-additional .dx-field-value-static {
  white-space: normal;
  padding: 21px 8px 20px;
}
.dx-swatch-additional .dx-fieldset {
  margin: 20px 16px;
  padding: 0;
}
.dx-swatch-additional .dx-rtl .dx-fieldset .dx-field-value:not(.dx-widget) > .dx-button,
.dx-swatch-additional .dx-rtl .dx-fieldset .dx-field-value:not(.dx-widget) > .dx-checkbox,
.dx-swatch-additional .dx-rtl .dx-fieldset .dx-field-value:not(.dx-widget) > .dx-switch,
.dx-swatch-additional .dx-rtl.dx-fieldset .dx-field-value:not(.dx-widget) > .dx-button,
.dx-swatch-additional .dx-rtl.dx-fieldset .dx-field-value:not(.dx-widget) > .dx-checkbox,
.dx-swatch-additional .dx-rtl.dx-fieldset .dx-field-value:not(.dx-widget) > .dx-switch {
  float: left;
}
.dx-swatch-additional .dx-fieldset-header {
  margin: 0 0 16px;
  font-size: 14px;
}
.dx-swatch-additional .dx-field {
  margin: 0 0 20px;
}
.dx-swatch-additional .dx-field:last-of-type {
  margin: 0;
}
.dx-swatch-additional .dx-device-mobile .dx-fieldset {
  margin: 20px 15px;
  padding: 0;
}
.dx-swatch-additional .dx-rtl .dx-device-mobile .dx-fieldset .dx-field-value:not(.dx-widget) > .dx-button,
.dx-swatch-additional .dx-rtl .dx-device-mobile .dx-fieldset .dx-field-value:not(.dx-widget) > .dx-checkbox,
.dx-swatch-additional .dx-rtl .dx-device-mobile .dx-fieldset .dx-field-value:not(.dx-widget) > .dx-switch,
.dx-swatch-additional .dx-rtl.dx-device-mobile .dx-fieldset .dx-field-value:not(.dx-widget) > .dx-button,
.dx-swatch-additional .dx-rtl.dx-device-mobile .dx-fieldset .dx-field-value:not(.dx-widget) > .dx-checkbox,
.dx-swatch-additional .dx-rtl.dx-device-mobile .dx-fieldset .dx-field-value:not(.dx-widget) > .dx-switch {
  float: left;
}
.dx-swatch-additional .dx-device-mobile .dx-fieldset-header {
  margin: 0 0 20px;
  font-size: 14px;
}
.dx-swatch-additional .dx-device-mobile .dx-field {
  margin: 0 0 10px;
}
.dx-swatch-additional .dx-device-mobile .dx-field:last-of-type {
  margin: 0;
}
.dx-swatch-additional .dx-theme-marker {
  font-family: "dx.material.orange-dark-compact";
}
.dx-swatch-additional .dx-theme-accent-as-text-color {
  color: #ff5722!important;
}
.dx-swatch-additional .dx-theme-text-color {
  color: #fff!important;
}
.dx-swatch-additional .dx-theme-background-color-as-text-color {
  color: #363640!important;
}
.dx-swatch-additional .dx-theme-border-color-as-text-color {
  color: #515159!important;
}
.dx-swatch-additional .dx-theme-accent-as-background-color {
  background-color: #ff5722!important;
  fill: #ff5722!important;
}
.dx-swatch-additional .dx-theme-text-color-as-background-color {
  background-color: #fff!important;
  fill: #fff!important;
}
.dx-swatch-additional .dx-theme-background-color {
  background-color: #363640!important;
  fill: #363640!important;
}
.dx-swatch-additional .dx-theme-border-color-as-background-color {
  background-color: #515159!important;
  fill: #515159!important;
}
.dx-swatch-additional .dx-theme-accent-as-border-color {
  border-color: #ff5722!important;
}
.dx-swatch-additional .dx-theme-text-color-as-border-color {
  border-color: #fff!important;
}
.dx-swatch-additional .dx-theme-background-color-as-border-color {
  border-color: #363640!important;
}
.dx-swatch-additional .dx-theme-border-color {
  border-color: #515159!important;
}
.dx-swatch-additional .dx-inkripple {
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  left: 0;
  pointer-events: none;
}
.dx-swatch-additional .dx-inkripple-wave {
  position: absolute;
  background-color: rgba(0,0,0,.16);
  border-radius: 50%;
  opacity: 0;
  -webkit-transform: scale(0);
  transform: scale(0);
  pointer-events: none;
}
.dx-swatch-additional .dx-inkripple-showing {
  -webkit-transition: -webkit-transform .1s linear;
  transition: transform .1s linear;
  transition: transform .1s linear,-webkit-transform .1s linear;
  opacity: 1;
  -webkit-transform: scale(1);
  transform: scale(1);
}
.dx-swatch-additional .dx-inkripple-hiding {
  -webkit-transition: opacity .1s linear,-webkit-transform .1s linear;
  transition: transform .1s linear,opacity .1s linear,-webkit-transform .1s linear;
  -webkit-transform: scale(1.01);
  transform: scale(1.01);
  opacity: 0;
}
.dx-swatch-additional .dx-resizable {
  display: block;
  position: relative;
}
.dx-swatch-additional .dx-resizable-handle {
  position: absolute;
  z-index: 50;
}
.dx-swatch-additional .dx-state-disabled .dx-resizable-handle {
  cursor: default;
}
.dx-swatch-additional .dx-resizable-handle-left,
.dx-swatch-additional .dx-resizable-handle-right {
  top: 0;
  height: 100%;
  width: 3px;
}
.dx-swatch-additional .dx-resizable-handle-left {
  left: 0;
  cursor: e-resize;
}
.dx-swatch-additional .dx-resizable-handle-right {
  right: 0;
  cursor: e-resize;
}
.dx-swatch-additional .dx-resizable-handle-bottom,
.dx-swatch-additional .dx-resizable-handle-top {
  left: 0;
  width: 100%;
  height: 3px;
}
.dx-swatch-additional .dx-resizable-handle-top {
  top: 0;
  cursor: s-resize;
}
.dx-swatch-additional .dx-resizable-handle-bottom {
  bottom: 0;
  cursor: s-resize;
}
.dx-swatch-additional .dx-resizable-handle-corner-bottom-left,
.dx-swatch-additional .dx-resizable-handle-corner-bottom-right,
.dx-swatch-additional .dx-resizable-handle-corner-top-left,
.dx-swatch-additional .dx-resizable-handle-corner-top-right {
  width: 6px;
  height: 6px;
}
.dx-swatch-additional .dx-resizable-handle-corner-bottom-right,
.dx-swatch-additional .dx-resizable-handle-corner-top-left {
  cursor: se-resize;
}
.dx-swatch-additional .dx-resizable-handle-corner-bottom-left,
.dx-swatch-additional .dx-resizable-handle-corner-top-right {
  cursor: ne-resize;
}
.dx-swatch-additional .dx-resizable-handle-corner-bottom-left,
.dx-swatch-additional .dx-resizable-handle-corner-top-left {
  left: 0;
  border-bottom-right-radius: 100%;
}
.dx-swatch-additional .dx-resizable-handle-corner-bottom-right,
.dx-swatch-additional .dx-resizable-handle-corner-top-right {
  right: 0;
  border-bottom-left-radius: 100%;
}
.dx-swatch-additional .dx-resizable-handle-corner-top-left,
.dx-swatch-additional .dx-resizable-handle-corner-top-right {
  top: 0;
}
.dx-swatch-additional .dx-resizable-handle-corner-bottom-left,
.dx-swatch-additional .dx-resizable-handle-corner-bottom-right {
  bottom: 0;
}
.dx-swatch-additional .dx-draggable {
  left: 0;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
}
.dx-swatch-additional .dx-draggable.dx-state-disabled,
.dx-swatch-additional .dx-state-disabled .dx-draggable {
  cursor: default;
}
.dx-swatch-additional .dx-draggable-clone {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 2147483647;
}
.dx-swatch-additional .dx-clearfix::after,
.dx-swatch-additional .dx-clearfix::before {
  display: table;
  content: "";
  line-height: 0;
}
.dx-swatch-additional .dx-clearfix::after {
  clear: both;
}
.dx-swatch-additional .dx-translate-disabled {
  -webkit-transform: none!important;
  transform: none!important;
}
.dx-swatch-additional .dx-hidden-input {
  position: fixed;
  top: -10px;
  left: -10px;
  width: 0;
  height: 0;
}
.dx-swatch-additional .dx-user-select {
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
}
.dx-swatch-additional .dx-hidden,
.dx-swatch-additional .dx-state-invisible {
  display: none!important;
}
.dx-swatch-additional .dx-gesture-cover {
  -webkit-transform: translate3d(0,0,0);
  transform: translate3d(0,0,0);
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  opacity: 0;
  z-index: 2147483647;
}
.dx-swatch-additional .dx-animating {
  pointer-events: none;
}
.dx-swatch-additional .dx-fade-animation.dx-enter,
.dx-swatch-additional .dx-fade-animation.dx-leave.dx-leave-active,
.dx-swatch-additional .dx-no-direction.dx-enter,
.dx-swatch-additional .dx-no-direction.dx-leave.dx-leave-active {
  opacity: 0;
}
.dx-swatch-additional .dx-fade-animation.dx-enter.dx-enter-active,
.dx-swatch-additional .dx-fade-animation.dx-leave,
.dx-swatch-additional .dx-no-direction.dx-enter.dx-enter-active,
.dx-swatch-additional .dx-no-direction.dx-leave {
  opacity: 1;
}
.dx-swatch-additional .dx-overflow-animation.dx-enter.dx-forward {
  -webkit-transform: translate3d(100%,0,0);
  transform: translate3d(100%,0,0);
  z-index: 2;
}
.dx-swatch-additional .dx-overflow-animation.dx-enter.dx-enter-active.dx-forward {
  -webkit-transform: translate3d(0,0,0);
  transform: translate3d(0,0,0);
  z-index: 2;
}
.dx-swatch-additional .dx-overflow-animation.dx-enter.dx-backward,
.dx-swatch-additional .dx-overflow-animation.dx-enter.dx-enter-active.dx-backward,
.dx-swatch-additional .dx-overflow-animation.dx-leave.dx-forward,
.dx-swatch-additional .dx-overflow-animation.dx-leave.dx-leave-active.dx-forward {
  -webkit-transform: translate3d(0,0,0);
  transform: translate3d(0,0,0);
  z-index: 1;
}
.dx-swatch-additional .dx-overflow-animation.dx-leave.dx-backward {
  -webkit-transform: translate3d(0,0,0);
  transform: translate3d(0,0,0);
  z-index: 2;
}
.dx-swatch-additional .dx-overflow-animation.dx-leave.dx-leave-active.dx-backward {
  -webkit-transform: translate3d(100%,0,0);
  transform: translate3d(100%,0,0);
  z-index: 2;
}
.dx-swatch-additional .dx-slide-animation.dx-enter.dx-forward {
  -webkit-transform: translate3d(100%,0,0);
  transform: translate3d(100%,0,0);
}
.dx-swatch-additional .dx-slide-animation.dx-enter.dx-enter-active.dx-forward {
  -webkit-transform: translate3d(0,0,0);
  transform: translate3d(0,0,0);
}
.dx-swatch-additional .dx-slide-animation.dx-enter.dx-backward {
  -webkit-transform: translate3d(-100%,0,0);
  transform: translate3d(-100%,0,0);
}
.dx-swatch-additional .dx-slide-animation.dx-enter.dx-enter-active.dx-backward,
.dx-swatch-additional .dx-slide-animation.dx-leave.dx-forward {
  -webkit-transform: translate3d(0,0,0);
  transform: translate3d(0,0,0);
}
.dx-swatch-additional .dx-slide-animation.dx-leave.dx-leave-active.dx-forward {
  -webkit-transform: translate3d(-100%,0,0);
  transform: translate3d(-100%,0,0);
}
.dx-swatch-additional .dx-slide-animation.dx-leave.dx-backward {
  -webkit-transform: translate3d(0,0,0);
  transform: translate3d(0,0,0);
}
.dx-swatch-additional .dx-slide-animation.dx-leave.dx-leave-active.dx-backward {
  -webkit-transform: translate3d(100%,0,0);
  transform: translate3d(100%,0,0);
}
.dx-swatch-additional .dx-opendoor-animation.dx-enter.dx-forward {
  -webkit-transform: matrix3d(.71,0,.71,.001,0,1,0,0,-.71,0,.71,0,0,0,0,1);
  transform: matrix3d(.71,0,.71,.001,0,1,0,0,-.71,0,.71,0,0,0,0,1);
  -webkit-transform-origin: center left 0;
  transform-origin: center left 0;
  opacity: 0;
}
.dx-swatch-additional .dx-opendoor-animation.dx-enter.dx-enter-active.dx-backward,
.dx-swatch-additional .dx-opendoor-animation.dx-enter.dx-enter-active.dx-forward {
  -webkit-transform: none;
  transform: none;
  opacity: 1;
}
.dx-swatch-additional .dx-opendoor-animation.dx-leave.dx-backward,
.dx-swatch-additional .dx-opendoor-animation.dx-leave.dx-forward {
  -webkit-transform: none;
  transform: none;
  -webkit-transform-origin: center left 0;
  transform-origin: center left 0;
  opacity: 1;
}
.dx-swatch-additional .dx-opendoor-animation.dx-enter.dx-backward,
.dx-swatch-additional .dx-opendoor-animation.dx-leave.dx-leave-active.dx-forward {
  -webkit-transform: matrix3d(.5,0,.87,-.001,0,1,0,0,-.87,0,.5,0,0,0,0,1);
  transform: matrix3d(.5,0,.87,-.001,0,1,0,0,-.87,0,.5,0,0,0,0,1);
  -webkit-transform-origin: center left 0;
  transform-origin: center left 0;
  opacity: 0;
}
.dx-swatch-additional .dx-opendoor-animation.dx-leave.dx-leave-active.dx-backward {
  -webkit-transform: matrix3d(.71,0,.71,.001,0,1,0,0,-.71,0,.71,0,0,0,0,1);
  transform: matrix3d(.71,0,.71,.001,0,1,0,0,-.71,0,.71,0,0,0,0,1);
  opacity: 0;
}
.dx-swatch-additional .dx-win-pop-animation.dx-enter.dx-forward {
  -webkit-transform: scale(.5);
  transform: scale(.5);
  opacity: 0;
}
.dx-swatch-additional .dx-win-pop-animation.dx-enter.dx-enter-active.dx-forward {
  -webkit-transform: scale(1);
  transform: scale(1);
  opacity: 1;
}
.dx-swatch-additional .dx-win-pop-animation.dx-enter.dx-backward,
.dx-swatch-additional .dx-win-pop-animation.dx-leave.dx-leave-active.dx-forward {
  -webkit-transform: scale(1.5);
  transform: scale(1.5);
  opacity: 0;
}
.dx-swatch-additional .dx-win-pop-animation.dx-enter.dx-enter-active.dx-backward {
  -webkit-transform: scale(1);
  transform: scale(1);
  opacity: 1;
}
.dx-swatch-additional .dx-win-pop-animation.dx-leave.dx-leave-active.dx-backward {
  -webkit-transform: scale(.5);
  transform: scale(.5);
  opacity: 0;
}
.dx-swatch-additional .dx-android-pop-animation.dx-enter.dx-forward,
.dx-swatch-additional .dx-android-pop-animation.dx-leave.dx-leave-active.dx-backward {
  -webkit-transform: translate3d(0,150px,0);
  transform: translate3d(0,150px,0);
  opacity: 0;
}
.dx-swatch-additional .dx-android-pop-animation.dx-enter.dx-enter-active.dx-forward,
.dx-swatch-additional .dx-android-pop-animation.dx-leave.dx-backward {
  -webkit-transform: translate3d(0,0,0);
  transform: translate3d(0,0,0);
  opacity: 1;
}
.dx-swatch-additional .dx-android-pop-animation.dx-enter.dx-forward,
.dx-swatch-additional .dx-android-pop-animation.dx-leave.dx-backward {
  z-index: 1;
}
.dx-swatch-additional .dx-ios7-slide-animation.dx-enter.dx-forward {
  z-index: 2;
  -webkit-transform: translate3d(100%,0,0);
  transform: translate3d(100%,0,0);
}
.dx-swatch-additional .dx-ios7-slide-animation.dx-enter.dx-enter-active.dx-forward {
  z-index: 2;
  -webkit-transform: translate3d(0,0,0);
  transform: translate3d(0,0,0);
}
.dx-swatch-additional .dx-ios7-slide-animation.dx-enter.dx-backward {
  -webkit-transform: translate3d(-20%,0,0);
  transform: translate3d(-20%,0,0);
  z-index: 1;
}
.dx-swatch-additional .dx-ios7-slide-animation.dx-enter.dx-enter-active.dx-backward {
  -webkit-transform: translate3d(0,0,0);
  transform: translate3d(0,0,0);
  z-index: 1;
}
.dx-swatch-additional .dx-ios7-slide-animation.dx-leave.dx-forward {
  z-index: 1;
  -webkit-transform: translate3d(0,0,0);
  transform: translate3d(0,0,0);
}
.dx-swatch-additional .dx-ios7-slide-animation.dx-leave.dx-leave-active.dx-forward {
  -webkit-transform: translate3d(-20%,0,0);
  transform: translate3d(-20%,0,0);
  z-index: 1;
}
.dx-swatch-additional .dx-ios7-slide-animation.dx-leave.dx-backward {
  z-index: 2;
}
.dx-swatch-additional .dx-ios7-slide-animation.dx-leave.dx-leave-active.dx-backward {
  -webkit-transform: translate3d(100%,0,0);
  transform: translate3d(100%,0,0);
  z-index: 2;
}
.dx-swatch-additional .dx-ios7-toolbar-animation.dx-enter.dx-forward {
  -webkit-transform: translate3d(40%,0,0);
  transform: translate3d(40%,0,0);
  opacity: 0;
  z-index: 2;
}
.dx-swatch-additional .dx-ios7-toolbar-animation.dx-enter.dx-enter-active.dx-forward {
  -webkit-transform: translate3d(0,0,0);
  transform: translate3d(0,0,0);
  opacity: 1;
  z-index: 2;
}
.dx-swatch-additional .dx-ios7-toolbar-animation.dx-enter.dx-backward {
  -webkit-transform: translate3d(-40%,0,0);
  transform: translate3d(-40%,0,0);
  opacity: 0;
  z-index: 1;
}
.dx-swatch-additional .dx-ios7-toolbar-animation.dx-enter.dx-enter-active.dx-backward,
.dx-swatch-additional .dx-ios7-toolbar-animation.dx-leave.dx-forward {
  -webkit-transform: translate3d(0,0,0);
  transform: translate3d(0,0,0);
  opacity: 1;
  z-index: 1;
}
.dx-swatch-additional .dx-ios7-toolbar-animation.dx-leave.dx-leave-active.dx-forward {
  -webkit-transform: translate3d(-40%,0,0);
  transform: translate3d(-40%,0,0);
  opacity: 0;
  z-index: 1;
}
.dx-swatch-additional .dx-ios7-toolbar-animation.dx-leave.dx-backward {
  -webkit-transform: translate3d(0,0,0);
  transform: translate3d(0,0,0);
  opacity: 1;
  z-index: 2;
}
.dx-swatch-additional .dx-ios7-toolbar-animation.dx-leave.dx-leave-active.dx-backward {
  -webkit-transform: translate3d(40%,0,0);
  transform: translate3d(40%,0,0);
  opacity: 0;
  z-index: 2;
}
.dx-swatch-additional .dx-drop-animation.dx-enter,
.dx-swatch-additional .dx-drop-animation.dx-leave.dx-leave-active {
  -webkit-transform: translate3d(0,-120%,0);
  transform: translate3d(0,-120%,0);
}
.dx-swatch-additional .dx-drop-animation.dx-enter.dx-enter-active,
.dx-swatch-additional .dx-drop-animation.dx-leave {
  -webkit-transform: translate3d(0,0,0);
  transform: translate3d(0,0,0);
}
.dx-swatch-additional .dx-3d-drop-animation.dx-enter,
.dx-swatch-additional .dx-3d-drop-animation.dx-leave.dx-leave-active {
  -webkit-transform: rotate3d(1,0,0,10deg) translate3d(0,-10px,0) scale3d(1.1,1.1,1.1);
  transform: rotate3d(1,0,0,10deg) translate3d(0,-10px,0) scale3d(1.1,1.1,1.1);
  opacity: 0;
}
.dx-swatch-additional .dx-3d-drop-animation.dx-enter.dx-enter-active,
.dx-swatch-additional .dx-3d-drop-animation.dx-leave {
  -webkit-transform: rotate3d(1,0,0,0) translate3d(0,0,0) scale3d(1,1,1);
  transform: rotate3d(1,0,0,0) translate3d(0,0,0) scale3d(1,1,1);
  opacity: 1;
}
.dx-swatch-additional .dx-fade-drop-animation.dx-enter,
.dx-swatch-additional .dx-fade-drop-animation.dx-leave.dx-leave-active {
  -webkit-transform: translate3d(0,-10px,0) scale3d(1.1,1.1,1.1);
  transform: translate3d(0,-10px,0) scale3d(1.1,1.1,1.1);
  opacity: 0;
}
.dx-swatch-additional .dx-fade-drop-animation.dx-enter.dx-enter-active,
.dx-swatch-additional .dx-fade-drop-animation.dx-leave {
  -webkit-transform: translate3d(0,0,0) scale3d(1,1,1);
  transform: translate3d(0,0,0) scale3d(1,1,1);
  opacity: 1;
}
.dx-swatch-additional .dx-fade-rise-animation.dx-enter,
.dx-swatch-additional .dx-fade-rise-animation.dx-leave.dx-leave-active {
  -webkit-transform: translate3d(0,10px,0) scale3d(1.1,1.1,1.1);
  transform: translate3d(0,10px,0) scale3d(1.1,1.1,1.1);
  opacity: 0;
}
.dx-swatch-additional .dx-fade-rise-animation.dx-enter.dx-enter-active,
.dx-swatch-additional .dx-fade-rise-animation.dx-leave {
  -webkit-transform: translate3d(0,0,0) scale3d(1,1,1);
  transform: translate3d(0,0,0) scale3d(1,1,1);
  opacity: 1;
}
.dx-swatch-additional .dx-fade-slide-animation.dx-enter,
.dx-swatch-additional .dx-fade-slide-animation.dx-leave.dx-leave-active {
  -webkit-transform: translate3d(40%,0,0);
  transform: translate3d(40%,0,0);
  opacity: 0;
}
.dx-swatch-additional .dx-fade-slide-animation.dx-enter.dx-enter-active,
.dx-swatch-additional .dx-fade-slide-animation.dx-leave {
  -webkit-transform: translate3d(0,0,0);
  transform: translate3d(0,0,0);
  opacity: 1;
}
.dx-swatch-additional .dx-fade-zoom-animation.dx-enter,
.dx-swatch-additional .dx-fade-zoom-animation.dx-leave.dx-leave-active {
  -webkit-transform: scale3d(.3,.3,.3);
  transform: scale3d(.3,.3,.3);
  opacity: 0;
}
.dx-swatch-additional .dx-fade-zoom-animation.dx-enter.dx-enter-active,
.dx-swatch-additional .dx-fade-zoom-animation.dx-leave {
  -webkit-transform: scale3d(1,1,1);
  transform: scale3d(1,1,1);
  opacity: 1;
}
.dx-swatch-additional .dx-button-disabled {
  cursor: default;
}
.dx-swatch-additional .dx-button {
  display: inline-block;
  cursor: pointer;
  text-align: center;
  vertical-align: middle;
  max-width: 100%;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  -webkit-user-drag: none;
  min-width: 28px;
}
.dx-swatch-additional .dx-button .dx-icon {
  -webkit-user-drag: none;
  display: inline-block;
  vertical-align: middle;
}
.dx-swatch-additional .dx-button-content {
  height: 100%;
  max-height: 100%;
  line-height: 0;
}
.dx-swatch-additional .dx-button-content::after {
  display: inline-block;
  position: relative;
  height: 100%;
  content: "";
  vertical-align: middle;
  font-size: 0;
}
.dx-swatch-additional .dx-button-content > .dx-inkripple {
  display: none;
}
.dx-swatch-additional .dx-button-link {
  text-decoration: none;
}
.dx-swatch-additional .dx-button-text {
  display: inline;
  vertical-align: middle;
  line-height: 17px;
  text-transform: uppercase;
  font-weight: 500;
  letter-spacing: .04em;
}
.dx-swatch-additional .dx-button-submit-input {
  padding: 0;
  margin: 0;
  border: 0;
  height: 0;
  width: 0;
  font-size: 0;
  opacity: 0;
}
.dx-swatch-additional .dx-state-disabled .dx-button,
.dx-swatch-additional .dx-state-disabled.dx-button {
  cursor: default;
}
.dx-swatch-additional .dx-button a {
  text-decoration: none;
}
.dx-swatch-additional .dx-button .dx-button-content {
  padding: 5px;
  position: relative;
}
.dx-swatch-additional .dx-button .dx-icon {
  width: 18px;
  height: 18px;
  background-position: 0 0;
  background-size: 18px 18px;
  padding: 0;
  font-size: 18px;
  text-align: center;
  line-height: 18px;
  margin-right: 0;
  margin-left: 0;
  color: #fff;
}
.dx-swatch-additional .dx-button .dx-icon.dx-icon-right,
.dx-swatch-additional .dx-rtl .dx-button .dx-icon,
.dx-swatch-additional .dx-rtl.dx-button .dx-icon {
  margin-left: 0;
  margin-right: 0;
}
.dx-swatch-additional .dx-rtl .dx-button .dx-icon.dx-icon-right,
.dx-swatch-additional .dx-rtl.dx-button .dx-icon.dx-icon-right {
  margin-right: 0;
  margin-left: 0;
}
.dx-swatch-additional .dx-button-has-icon {
  min-width: 28px;
}
.dx-swatch-additional .dx-button-has-icon .dx-button-content {
  padding: 5px;
}
.dx-swatch-additional .dx-button-has-icon .dx-icon {
  width: 18px;
  height: 18px;
  background-position: 0 0;
  background-size: 18px 18px;
  padding: 0;
  font-size: 18px;
  text-align: center;
  line-height: 18px;
  margin-right: 0;
  margin-left: 0;
}
.dx-swatch-additional .dx-button-has-icon .dx-icon.dx-icon-right,
.dx-swatch-additional .dx-rtl .dx-button-has-icon .dx-icon,
.dx-swatch-additional .dx-rtl.dx-button-has-icon .dx-icon {
  margin-left: 0;
  margin-right: 0;
}
.dx-swatch-additional .dx-rtl .dx-button-has-icon .dx-icon.dx-icon-right,
.dx-swatch-additional .dx-rtl.dx-button-has-icon .dx-icon.dx-icon-right {
  margin-right: 0;
  margin-left: 0;
}
.dx-swatch-additional .dx-button-has-text {
  min-width: 48px;
}
.dx-swatch-additional .dx-button-has-text .dx-button-content {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding: 5px 16px;
}
.dx-swatch-additional .dx-button-has-text .dx-icon {
  width: 12px;
  height: 12px;
  background-position: 0 0;
  background-size: 12px 12px;
  padding: 0;
  font-size: 12px;
  text-align: center;
  line-height: 12px;
  margin-right: 9px;
  margin-left: 0;
}
.dx-swatch-additional .dx-button-has-text .dx-icon.dx-icon-right,
.dx-swatch-additional .dx-rtl .dx-button-has-text .dx-icon,
.dx-swatch-additional .dx-rtl.dx-button-has-text .dx-icon {
  margin-left: 9px;
  margin-right: 0;
}
.dx-swatch-additional .dx-rtl .dx-button-has-text .dx-icon.dx-icon-right,
.dx-swatch-additional .dx-rtl.dx-button-has-text .dx-icon.dx-icon-right {
  margin-right: 9px;
  margin-left: 0;
}
.dx-swatch-additional .dx-button-has-text.dx-button-mode-text .dx-button-content {
  padding: 5px;
}
.dx-swatch-additional .dx-button-has-text.dx-button-mode-text .dx-icon {
  width: 12px;
  height: 12px;
  background-position: 0 0;
  background-size: 12px 12px;
  padding: 0;
  font-size: 12px;
  text-align: center;
  line-height: 12px;
  margin-right: 9px;
  margin-left: 0;
}
.dx-swatch-additional .dx-button-has-text.dx-button-mode-text .dx-icon.dx-icon-right,
.dx-swatch-additional .dx-rtl .dx-button-has-text.dx-button-mode-text .dx-icon,
.dx-swatch-additional .dx-rtl.dx-button-has-text.dx-button-mode-text .dx-icon {
  margin-left: 9px;
  margin-right: 0;
}
.dx-swatch-additional .dx-rtl .dx-button-has-text.dx-button-mode-text .dx-icon.dx-icon-right,
.dx-swatch-additional .dx-rtl.dx-button-has-text.dx-button-mode-text .dx-icon.dx-icon-right {
  margin-right: 9px;
  margin-left: 0;
}
.dx-swatch-additional .dx-button-has-icon.dx-button-has-text .dx-button-content {
  padding: 5px 16px;
}
.dx-swatch-additional .dx-button-has-icon.dx-button-has-text .dx-icon {
  width: 12px;
  height: 12px;
  background-position: 0 0;
  background-size: 12px 12px;
  padding: 0;
  font-size: 12px;
  text-align: center;
  line-height: 12px;
  margin-right: 8px;
  margin-left: 0;
}
.dx-swatch-additional .dx-button-has-icon.dx-button-has-text .dx-icon.dx-icon-right,
.dx-swatch-additional .dx-rtl .dx-button-has-icon.dx-button-has-text .dx-icon,
.dx-swatch-additional .dx-rtl.dx-button-has-icon.dx-button-has-text .dx-icon {
  margin-left: 8px;
  margin-right: 0;
}
.dx-swatch-additional .dx-rtl .dx-button-has-icon.dx-button-has-text .dx-icon.dx-icon-right,
.dx-swatch-additional .dx-rtl.dx-button-has-icon.dx-button-has-text .dx-icon.dx-icon-right {
  margin-right: 8px;
  margin-left: 0;
}
.dx-swatch-additional .dx-button-has-icon.dx-button-has-text .dx-button-content {
  padding-left: 12px;
}
.dx-swatch-additional .dx-rtl .dx-button-has-icon.dx-button-has-text .dx-button-content {
  padding-left: 16px;
  padding-right: 12px;
}
.dx-swatch-additional .dx-button-has-icon.dx-button-has-text.dx-button-mode-text .dx-button-content {
  padding: 5px;
}
.dx-swatch-additional .dx-button-has-icon.dx-button-has-text.dx-button-mode-text .dx-icon {
  width: 12px;
  height: 12px;
  background-position: 0 0;
  background-size: 12px 12px;
  padding: 0;
  font-size: 12px;
  text-align: center;
  line-height: 12px;
  margin-right: 9px;
  margin-left: 0;
}
.dx-swatch-additional .dx-button-has-icon.dx-button-has-text.dx-button-mode-text .dx-icon.dx-icon-right,
.dx-swatch-additional .dx-rtl .dx-button-has-icon.dx-button-has-text.dx-button-mode-text .dx-icon,
.dx-swatch-additional .dx-rtl.dx-button-has-icon.dx-button-has-text.dx-button-mode-text .dx-icon {
  margin-left: 9px;
  margin-right: 0;
}
.dx-swatch-additional .dx-rtl .dx-button-has-icon.dx-button-has-text.dx-button-mode-text .dx-icon.dx-icon-right,
.dx-swatch-additional .dx-rtl.dx-button-has-icon.dx-button-has-text.dx-button-mode-text .dx-icon.dx-icon-right {
  margin-right: 9px;
  margin-left: 0;
}
.dx-swatch-additional .dx-button.dx-button-has-icon:not(.dx-button-has-text):not(.dx-shape-standard) {
  border-radius: 50%;
  -webkit-box-shadow: none;
  box-shadow: none;
}
.dx-swatch-additional .dx-button {
  height: 28px;
  position: relative;
  overflow: hidden;
  border-radius: 2px;
  background-color: #363640;
  color: #fff;
  -webkit-box-shadow: 0 1px 3px rgba(0,0,0,.4);
  box-shadow: 0 1px 3px rgba(0,0,0,.4);
}
.dx-swatch-additional .dx-button .dx-button-content .dx-inkripple {
  overflow: hidden;
  display: block;
}
.dx-swatch-additional .dx-button.dx-state-hover {
  background-color: #494956;
  -webkit-box-shadow: 0 1px 3px rgba(0,0,0,.4);
  box-shadow: 0 1px 3px rgba(0,0,0,.4);
}
.dx-swatch-additional .dx-button.dx-state-focused {
  background-color: #494956;
  -webkit-box-shadow: 0 1px 3px rgba(0,0,0,.48);
  box-shadow: 0 1px 3px rgba(0,0,0,.48);
}
.dx-swatch-additional .dx-button.dx-state-active {
  background-color: #7d7d92;
  -webkit-box-shadow: 0 4px 6px rgba(0,0,0,.48);
  box-shadow: 0 4px 6px rgba(0,0,0,.48);
}
.dx-swatch-additional .dx-button.dx-state-disabled {
  background: rgba(255,255,255,.1);
  -webkit-box-shadow: none;
  box-shadow: none;
}
.dx-swatch-additional .dx-button.dx-state-disabled .dx-icon {
  opacity: .6;
}
.dx-swatch-additional .dx-button.dx-state-disabled .dx-button-text {
  color: rgba(255,255,255,.35);
}
.dx-swatch-additional .dx-button .dx-inkripple-wave {
  background-color: rgba(255,255,255,.2);
}
.dx-swatch-additional .dx-button.dx-button-default {
  border-radius: 2px;
  background-color: #ff5722;
  color: rgba(0,0,0,.87);
  -webkit-box-shadow: 0 1px 3px rgba(0,0,0,.4);
  box-shadow: 0 1px 3px rgba(0,0,0,.4);
}
.dx-swatch-additional .dx-button.dx-button-default .dx-icon {
  color: rgba(0,0,0,.87);
}
.dx-swatch-additional .dx-button.dx-button-default.dx-state-hover {
  background-color: #ff784d;
  -webkit-box-shadow: 0 1px 3px rgba(0,0,0,.4);
  box-shadow: 0 1px 3px rgba(0,0,0,.4);
}
.dx-swatch-additional .dx-button.dx-button-default.dx-state-focused {
  background-color: #ff784d;
  -webkit-box-shadow: 0 1px 3px rgba(0,0,0,.48);
  box-shadow: 0 1px 3px rgba(0,0,0,.48);
}
.dx-swatch-additional .dx-button.dx-button-default.dx-state-active {
  background-color: #ffc4b1;
  -webkit-box-shadow: 0 4px 6px rgba(0,0,0,.48);
  box-shadow: 0 4px 6px rgba(0,0,0,.48);
}
.dx-swatch-additional .dx-button.dx-button-default.dx-state-disabled {
  background: rgba(255,255,255,.1);
  -webkit-box-shadow: none;
  box-shadow: none;
}
.dx-swatch-additional .dx-button.dx-button-default.dx-state-disabled .dx-icon {
  opacity: .6;
}
.dx-swatch-additional .dx-button.dx-button-default.dx-state-disabled .dx-button-text {
  color: rgba(255,255,255,.35);
}
.dx-swatch-additional .dx-button.dx-button-default .dx-inkripple-wave {
  background-color: rgba(0,0,0,.2);
}
.dx-swatch-additional .dx-button.dx-button-danger {
  border-radius: 2px;
  background-color: #f44336;
  color: rgba(0,0,0,.87);
  -webkit-box-shadow: 0 1px 3px rgba(0,0,0,.4);
  box-shadow: 0 1px 3px rgba(0,0,0,.4);
}
.dx-swatch-additional .dx-button.dx-button-danger .dx-icon {
  color: rgba(0,0,0,.87);
}
.dx-swatch-additional .dx-button.dx-button-danger.dx-state-hover {
  background-color: #f6695f;
  -webkit-box-shadow: 0 1px 3px rgba(0,0,0,.4);
  box-shadow: 0 1px 3px rgba(0,0,0,.4);
}
.dx-swatch-additional .dx-button.dx-button-danger.dx-state-focused {
  background-color: #f6695f;
  -webkit-box-shadow: 0 1px 3px rgba(0,0,0,.48);
  box-shadow: 0 1px 3px rgba(0,0,0,.48);
}
.dx-swatch-additional .dx-button.dx-button-danger.dx-state-active {
  background-color: #f99b94;
  -webkit-box-shadow: 0 4px 6px rgba(0,0,0,.48);
  box-shadow: 0 4px 6px rgba(0,0,0,.48);
}
.dx-swatch-additional .dx-button.dx-button-danger.dx-state-disabled {
  background: rgba(255,255,255,.1);
  -webkit-box-shadow: none;
  box-shadow: none;
}
.dx-swatch-additional .dx-button.dx-button-danger.dx-state-disabled .dx-icon {
  opacity: .6;
}
.dx-swatch-additional .dx-button.dx-button-danger.dx-state-disabled .dx-button-text {
  color: rgba(255,255,255,.35);
}
.dx-swatch-additional .dx-button.dx-button-danger .dx-inkripple-wave {
  background-color: rgba(0,0,0,.2);
}
.dx-swatch-additional .dx-button.dx-button-success {
  border-radius: 2px;
  background-color: #8bc34a;
  color: rgba(0,0,0,.87);
  -webkit-box-shadow: 0 1px 3px rgba(0,0,0,.4);
  box-shadow: 0 1px 3px rgba(0,0,0,.4);
}
.dx-swatch-additional .dx-button.dx-button-success .dx-icon {
  color: rgba(0,0,0,.87);
}
.dx-swatch-additional .dx-button.dx-button-success.dx-state-hover {
  background-color: #a0ce6b;
  -webkit-box-shadow: 0 1px 3px rgba(0,0,0,.4);
  box-shadow: 0 1px 3px rgba(0,0,0,.4);
}
.dx-swatch-additional .dx-button.dx-button-success.dx-state-focused {
  background-color: #a0ce6b;
  -webkit-box-shadow: 0 1px 3px rgba(0,0,0,.48);
  box-shadow: 0 1px 3px rgba(0,0,0,.48);
}
.dx-swatch-additional .dx-button.dx-button-success.dx-state-active {
  background-color: #bbdc95;
  -webkit-box-shadow: 0 4px 6px rgba(0,0,0,.48);
  box-shadow: 0 4px 6px rgba(0,0,0,.48);
}
.dx-swatch-additional .dx-button.dx-button-success.dx-state-disabled {
  background: rgba(255,255,255,.1);
  -webkit-box-shadow: none;
  box-shadow: none;
}
.dx-swatch-additional .dx-button.dx-button-success.dx-state-disabled .dx-icon {
  opacity: .6;
}
.dx-swatch-additional .dx-button.dx-button-success.dx-state-disabled .dx-button-text {
  color: rgba(255,255,255,.35);
}
.dx-swatch-additional .dx-button.dx-button-success .dx-inkripple-wave {
  background-color: rgba(0,0,0,.2);
}
.dx-swatch-additional .dx-button.dx-button-back {
  background-color: transparent;
  color: #fff;
  -webkit-box-shadow: none;
  box-shadow: none;
  border-radius: 50%;
  min-width: 28px;
}
.dx-swatch-additional .dx-button.dx-button-back.dx-state-focused,
.dx-swatch-additional .dx-button.dx-button-back.dx-state-hover {
  background-color: rgba(255,255,255,.08);
  -webkit-box-shadow: none;
  box-shadow: none;
}
.dx-swatch-additional .dx-button.dx-button-back.dx-state-active {
  background-color: rgba(255,255,255,.3);
  -webkit-box-shadow: none;
  box-shadow: none;
}
.dx-swatch-additional .dx-button.dx-button-back.dx-state-disabled {
  background: 0 0;
  -webkit-box-shadow: none;
  box-shadow: none;
}
.dx-swatch-additional .dx-button.dx-button-back.dx-state-disabled .dx-icon {
  opacity: .6;
}
.dx-swatch-additional .dx-button.dx-button-back.dx-state-disabled .dx-button-text {
  color: rgba(255,255,255,.35);
}
.dx-swatch-additional .dx-button.dx-button-back .dx-inkripple-wave {
  background-color: rgba(255,255,255,.2);
}
.dx-swatch-additional .dx-button.dx-button-back .dx-button-content {
  padding: 5px;
}
.dx-swatch-additional .dx-button.dx-button-back .dx-icon {
  color: #fff;
  width: 18px;
  height: 18px;
  background-position: 0 0;
  background-size: 18px 18px;
  padding: 0;
  font-size: 18px;
  text-align: center;
  line-height: 18px;
  margin-right: 0;
  margin-left: 0;
}
.dx-swatch-additional .dx-button.dx-button-back .dx-icon.dx-icon-right,
.dx-swatch-additional .dx-rtl .dx-button.dx-button-back .dx-icon,
.dx-swatch-additional .dx-rtl.dx-button.dx-button-back .dx-icon {
  margin-left: 0;
  margin-right: 0;
}
.dx-swatch-additional .dx-rtl .dx-button.dx-button-back .dx-icon.dx-icon-right,
.dx-swatch-additional .dx-rtl.dx-button.dx-button-back .dx-icon.dx-icon-right {
  margin-right: 0;
  margin-left: 0;
}
.dx-swatch-additional .dx-button.dx-button-back .dx-button-text {
  display: none;
}
.dx-swatch-additional .dx-button-mode-text {
  background-color: transparent;
  color: #fff;
  -webkit-box-shadow: none;
  box-shadow: none;
}
.dx-swatch-additional .dx-button-mode-text .dx-icon {
  color: #fff;
}
.dx-swatch-additional .dx-button-mode-text.dx-state-focused,
.dx-swatch-additional .dx-button-mode-text.dx-state-hover {
  background-color: rgba(255,255,255,.08);
  -webkit-box-shadow: none;
  box-shadow: none;
}
.dx-swatch-additional .dx-button-mode-text.dx-state-active {
  background-color: rgba(255,255,255,.3);
  -webkit-box-shadow: none;
  box-shadow: none;
}
.dx-swatch-additional .dx-button-mode-text.dx-state-disabled {
  background: 0 0;
  -webkit-box-shadow: none;
  box-shadow: none;
}
.dx-swatch-additional .dx-button-mode-text.dx-state-disabled .dx-icon {
  opacity: .6;
}
.dx-swatch-additional .dx-button-mode-text.dx-state-disabled .dx-button-text {
  color: rgba(255,255,255,.35);
}
.dx-swatch-additional .dx-button-mode-text .dx-inkripple-wave {
  background-color: rgba(255,255,255,.2);
}
.dx-swatch-additional .dx-button-mode-text.dx-button-default {
  background-color: transparent;
  color: #ff5722;
  -webkit-box-shadow: none;
  box-shadow: none;
}
.dx-swatch-additional .dx-button-mode-text.dx-button-default .dx-icon {
  color: #ff5722;
}
.dx-swatch-additional .dx-button-mode-text.dx-button-default.dx-state-focused,
.dx-swatch-additional .dx-button-mode-text.dx-button-default.dx-state-hover {
  background-color: rgba(255,87,34,.08);
  -webkit-box-shadow: none;
  box-shadow: none;
}
.dx-swatch-additional .dx-button-mode-text.dx-button-default.dx-state-active {
  background-color: rgba(255,87,34,.36);
  -webkit-box-shadow: none;
  box-shadow: none;
}
.dx-swatch-additional .dx-button-mode-text.dx-button-default.dx-state-disabled {
  background: 0 0;
  -webkit-box-shadow: none;
  box-shadow: none;
}
.dx-swatch-additional .dx-button-mode-text.dx-button-default.dx-state-disabled .dx-icon {
  opacity: .6;
}
.dx-swatch-additional .dx-button-mode-text.dx-button-default.dx-state-disabled .dx-button-text {
  color: rgba(255,255,255,.35);
}
.dx-swatch-additional .dx-button-mode-text.dx-button-default .dx-inkripple-wave {
  background-color: rgba(255,87,34,.2);
}
.dx-swatch-additional .dx-button-mode-text.dx-button-danger {
  background-color: transparent;
  color: #f44336;
  -webkit-box-shadow: none;
  box-shadow: none;
}
.dx-swatch-additional .dx-button-mode-text.dx-button-danger .dx-icon {
  color: #f44336;
}
.dx-swatch-additional .dx-button-mode-text.dx-button-danger.dx-state-focused,
.dx-swatch-additional .dx-button-mode-text.dx-button-danger.dx-state-hover {
  background-color: rgba(244,67,54,.08);
  -webkit-box-shadow: none;
  box-shadow: none;
}
.dx-swatch-additional .dx-button-mode-text.dx-button-danger.dx-state-active {
  background-color: rgba(244,67,54,.36);
  -webkit-box-shadow: none;
  box-shadow: none;
}
.dx-swatch-additional .dx-button-mode-text.dx-button-danger.dx-state-disabled {
  background: 0 0;
  -webkit-box-shadow: none;
  box-shadow: none;
}
.dx-swatch-additional .dx-button-mode-text.dx-button-danger.dx-state-disabled .dx-icon {
  opacity: .6;
}
.dx-swatch-additional .dx-button-mode-text.dx-button-danger.dx-state-disabled .dx-button-text {
  color: rgba(255,255,255,.35);
}
.dx-swatch-additional .dx-button-mode-text.dx-button-danger .dx-inkripple-wave {
  background-color: rgba(244,67,54,.2);
}
.dx-swatch-additional .dx-button-mode-text.dx-button-success {
  background-color: transparent;
  color: #8bc34a;
  -webkit-box-shadow: none;
  box-shadow: none;
}
.dx-swatch-additional .dx-button-mode-text.dx-button-success .dx-icon {
  color: #8bc34a;
}
.dx-swatch-additional .dx-button-mode-text.dx-button-success.dx-state-focused,
.dx-swatch-additional .dx-button-mode-text.dx-button-success.dx-state-hover {
  background-color: rgba(139,195,74,.08);
  -webkit-box-shadow: none;
  box-shadow: none;
}
.dx-swatch-additional .dx-button-mode-text.dx-button-success.dx-state-active {
  background-color: rgba(139,195,74,.36);
  -webkit-box-shadow: none;
  box-shadow: none;
}
.dx-swatch-additional .dx-button-mode-text.dx-button-success.dx-state-disabled {
  background: 0 0;
  -webkit-box-shadow: none;
  box-shadow: none;
}
.dx-swatch-additional .dx-button-mode-text.dx-button-success.dx-state-disabled .dx-icon {
  opacity: .6;
}
.dx-swatch-additional .dx-button-mode-text.dx-button-success.dx-state-disabled .dx-button-text {
  color: rgba(255,255,255,.35);
}
.dx-swatch-additional .dx-button-mode-text.dx-button-success .dx-inkripple-wave {
  background-color: rgba(139,195,74,.2);
}
.dx-swatch-additional .dx-button-mode-outlined {
  background-color: transparent;
  color: #fff;
  -webkit-box-shadow: none;
  box-shadow: none;
  border: 1px solid rgba(255,255,255,.24);
}
.dx-swatch-additional .dx-button-mode-outlined .dx-icon {
  color: #fff;
}
.dx-swatch-additional .dx-button-mode-outlined.dx-state-focused,
.dx-swatch-additional .dx-button-mode-outlined.dx-state-hover {
  background-color: rgba(255,255,255,.08);
  -webkit-box-shadow: none;
  box-shadow: none;
}
.dx-swatch-additional .dx-button-mode-outlined.dx-state-active {
  background-color: rgba(255,255,255,.3);
  -webkit-box-shadow: none;
  box-shadow: none;
}
.dx-swatch-additional .dx-button-mode-outlined.dx-state-disabled {
  background: 0 0;
  -webkit-box-shadow: none;
  box-shadow: none;
}
.dx-swatch-additional .dx-button-mode-outlined.dx-state-disabled .dx-icon {
  opacity: .6;
}
.dx-swatch-additional .dx-button-mode-outlined.dx-state-disabled .dx-button-text {
  color: rgba(255,255,255,.35);
}
.dx-swatch-additional .dx-button-mode-outlined .dx-inkripple-wave {
  background-color: rgba(255,255,255,.2);
}
.dx-swatch-additional .dx-button-mode-outlined.dx-button-default {
  background-color: transparent;
  color: #ff5722;
  -webkit-box-shadow: none;
  box-shadow: none;
  border: 1px solid #ff5722;
}
.dx-swatch-additional .dx-button-mode-outlined.dx-button-default .dx-icon {
  color: #ff5722;
}
.dx-swatch-additional .dx-button-mode-outlined.dx-button-default.dx-state-focused,
.dx-swatch-additional .dx-button-mode-outlined.dx-button-default.dx-state-hover {
  background-color: rgba(255,87,34,.08);
  -webkit-box-shadow: none;
  box-shadow: none;
}
.dx-swatch-additional .dx-button-mode-outlined.dx-button-default.dx-state-active {
  background-color: rgba(255,87,34,.36);
  -webkit-box-shadow: none;
  box-shadow: none;
}
.dx-swatch-additional .dx-button-mode-outlined.dx-button-default.dx-state-disabled {
  background: 0 0;
  -webkit-box-shadow: none;
  box-shadow: none;
}
.dx-swatch-additional .dx-button-mode-outlined.dx-button-default.dx-state-disabled .dx-icon {
  opacity: .6;
}
.dx-swatch-additional .dx-button-mode-outlined.dx-button-default.dx-state-disabled .dx-button-text {
  color: rgba(255,255,255,.35);
}
.dx-swatch-additional .dx-button-mode-outlined.dx-button-default .dx-inkripple-wave {
  background-color: rgba(255,87,34,.2);
}
.dx-swatch-additional .dx-button-mode-outlined.dx-button-danger {
  background-color: transparent;
  color: #f44336;
  -webkit-box-shadow: none;
  box-shadow: none;
  border: 1px solid #f44336;
}
.dx-swatch-additional .dx-button-mode-outlined.dx-button-danger .dx-icon {
  color: #f44336;
}
.dx-swatch-additional .dx-button-mode-outlined.dx-button-danger.dx-state-focused,
.dx-swatch-additional .dx-button-mode-outlined.dx-button-danger.dx-state-hover {
  background-color: rgba(244,67,54,.08);
  -webkit-box-shadow: none;
  box-shadow: none;
}
.dx-swatch-additional .dx-button-mode-outlined.dx-button-danger.dx-state-active {
  background-color: rgba(244,67,54,.36);
  -webkit-box-shadow: none;
  box-shadow: none;
}
.dx-swatch-additional .dx-button-mode-outlined.dx-button-danger.dx-state-disabled {
  background: 0 0;
  -webkit-box-shadow: none;
  box-shadow: none;
}
.dx-swatch-additional .dx-button-mode-outlined.dx-button-danger.dx-state-disabled .dx-icon {
  opacity: .6;
}
.dx-swatch-additional .dx-button-mode-outlined.dx-button-danger.dx-state-disabled .dx-button-text {
  color: rgba(255,255,255,.35);
}
.dx-swatch-additional .dx-button-mode-outlined.dx-button-danger .dx-inkripple-wave {
  background-color: rgba(244,67,54,.2);
}
.dx-swatch-additional .dx-button-mode-outlined.dx-button-success {
  background-color: transparent;
  color: #8bc34a;
  -webkit-box-shadow: none;
  box-shadow: none;
  border: 1px solid #8bc34a;
}
.dx-swatch-additional .dx-button-mode-outlined.dx-button-success .dx-icon {
  color: #8bc34a;
}
.dx-swatch-additional .dx-button-mode-outlined.dx-button-success.dx-state-focused,
.dx-swatch-additional .dx-button-mode-outlined.dx-button-success.dx-state-hover {
  background-color: rgba(139,195,74,.08);
  -webkit-box-shadow: none;
  box-shadow: none;
}
.dx-swatch-additional .dx-button-mode-outlined.dx-button-success.dx-state-active {
  background-color: rgba(139,195,74,.36);
  -webkit-box-shadow: none;
  box-shadow: none;
}
.dx-swatch-additional .dx-button-mode-outlined.dx-button-success.dx-state-disabled {
  background: 0 0;
  -webkit-box-shadow: none;
  box-shadow: none;
}
.dx-swatch-additional .dx-button-mode-outlined.dx-button-success.dx-state-disabled .dx-icon {
  opacity: .6;
}
.dx-swatch-additional .dx-button-mode-outlined.dx-button-success.dx-state-disabled .dx-button-text {
  color: rgba(255,255,255,.35);
}
.dx-swatch-additional .dx-button-mode-outlined.dx-button-success .dx-inkripple-wave {
  background-color: rgba(139,195,74,.2);
}
.dx-swatch-additional .dx-checkbox {
  display: inline-block;
  cursor: pointer;
  -webkit-user-drag: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
}
.dx-swatch-additional .dx-checkbox.dx-state-readonly {
  cursor: default;
}
.dx-swatch-additional .dx-checkbox-icon {
  -ms-flex-negative: 0;
  flex-shrink: 0;
  position: relative;
  background-position: 0 0;
  background-size: cover;
  background-repeat: no-repeat;
}
.dx-swatch-additional .dx-checkbox-container {
  overflow: hidden;
  white-space: nowrap;
  height: 100%;
  width: 100%;
  vertical-align: middle;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  text-overflow: clip;
}
.dx-swatch-additional .dx-checkbox-text {
  text-align: left;
  white-space: pre-wrap;
  word-wrap: break-word;
  line-height: normal;
  padding-left: 5px;
}
.dx-swatch-additional .dx-rtl .dx-checkbox-text,
.dx-swatch-additional .dx-rtl.dx-checkbox-text {
  margin: 0;
  padding: 0 5px 0 0;
  text-align: right;
}
.dx-swatch-additional .dx-state-disabled .dx-checkbox,
.dx-swatch-additional .dx-state-disabled.dx-checkbox {
  cursor: default;
}
.dx-swatch-additional .dx-checkbox {
  line-height: 0;
}
.dx-swatch-additional .dx-checkbox .dx-checkbox-container {
  overflow: visible;
}
.dx-swatch-additional .dx-checkbox.dx-state-disabled,
.dx-swatch-additional .dx-checkbox.dx-state-readonly {
  border-color: rgba(255,255,255,.26);
}
.dx-swatch-additional .dx-checkbox.dx-state-active .dx-checkbox-icon::after,
.dx-swatch-additional .dx-checkbox.dx-state-focused .dx-checkbox-icon::after {
  background-color: rgba(255,255,255,.1);
  -webkit-transform: scale(1);
  transform: scale(1);
}
.dx-swatch-additional .dx-checkbox.dx-checkbox-checked.dx-state-active .dx-checkbox-icon::after,
.dx-swatch-additional .dx-checkbox.dx-checkbox-checked.dx-state-focused .dx-checkbox-icon::after,
.dx-swatch-additional .dx-checkbox.dx-checkbox-indeterminate.dx-state-active .dx-checkbox-icon::after,
.dx-swatch-additional .dx-checkbox.dx-checkbox-indeterminate.dx-state-focused .dx-checkbox-icon::after {
  background-color: rgba(255,87,34,.1);
  -webkit-transform: scale(1);
  transform: scale(1);
}
.dx-swatch-additional .dx-checkbox.dx-checkbox-checked.dx-state-disabled .dx-checkbox-icon,
.dx-swatch-additional .dx-checkbox.dx-checkbox-checked.dx-state-readonly .dx-checkbox-icon,
.dx-swatch-additional .dx-checkbox.dx-checkbox-indeterminate.dx-state-disabled .dx-checkbox-icon,
.dx-swatch-additional .dx-checkbox.dx-checkbox-indeterminate.dx-state-readonly .dx-checkbox-icon {
  background-color: rgba(255,255,255,.26);
}
.dx-swatch-additional .dx-checkbox-checked.dx-state-readonly.dx-state-focused .dx-checkbox-icon::after,
.dx-swatch-additional .dx-checkbox-indeterminate.dx-state-readonly.dx-state-focused .dx-checkbox-icon::after,
.dx-swatch-additional .dx-checkbox.dx-state-readonly.dx-state-focused .dx-checkbox-icon::after {
  background-color: rgba(255,255,255,.1);
  -webkit-transform: scale(1);
  transform: scale(1);
}
.dx-swatch-additional .dx-checkbox-icon {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255,255,255,.54);
  border-radius: 2px;
}
.dx-swatch-additional .dx-checkbox-icon::after {
  content: "";
  width: 2.14em;
  height: 2.14em;
  top: 50%;
  left: 50%;
  margin-top: -1.07em;
  margin-left: -1.07em;
  border-radius: 50%;
  display: block;
  position: absolute;
  z-index: 1;
  -webkit-transform: scale(.5);
  transform: scale(.5);
  -webkit-transition: .4s cubic-bezier(.23, 1, .32, 1);
  transition: .4s cubic-bezier(.23, 1, .32, 1);
}
.dx-swatch-additional .dx-checkbox-icon::before {
  z-index: 2;
}
.dx-swatch-additional .dx-checkbox-checked .dx-checkbox-icon {
  color: #363640;
  background-color: #ff5722;
  border: none;
  font: 14px/1em DXIcons;
  text-align: center;
}
.dx-swatch-additional .dx-checkbox-checked .dx-checkbox-icon::before {
  content: "\f005";
  position: relative;
  display: block;
  width: 1em;
  top: 50%;
  margin-top: -.5em;
  left: 50%;
  margin-left: -.5em;
}
.dx-swatch-additional .dx-rtl .dx-checkbox-checked .dx-checkbox-icon::before,
.dx-swatch-additional .dx-rtl.dx-checkbox-checked .dx-checkbox-icon::before {
  left: 0;
  margin-left: 0;
  right: 50%;
  margin-right: -.5em;
}
.dx-swatch-additional .dx-checkbox-indeterminate .dx-checkbox-icon {
  background-color: #ff5722;
  color: #363640;
  border: none;
  font: 18px/1em DXIcons;
  text-align: center;
}
.dx-swatch-additional .dx-checkbox-indeterminate .dx-checkbox-icon::before {
  content: "\f074";
  position: relative;
  display: block;
  width: 1em;
  top: 50%;
  margin-top: -.5em;
  left: 50%;
  margin-left: -.5em;
}
.dx-swatch-additional .dx-rtl .dx-checkbox-indeterminate .dx-checkbox-icon::before,
.dx-swatch-additional .dx-rtl.dx-checkbox-indeterminate .dx-checkbox-icon::before {
  left: 0;
  margin-left: 0;
  right: 50%;
  margin-right: -.5em;
}
.dx-swatch-additional .dx-invalid .dx-checkbox-icon {
  border: 2px solid #f44336;
}
.dx-swatch-additional .dx-invalid.dx-state-focused .dx-checkbox-icon::after {
  background-color: rgba(244,67,54,.1);
  -webkit-transform: scale(1);
  transform: scale(1);
}
.dx-swatch-additional .dx-tabs-ie-hack a {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #fff;
  color: #fff;
  text-decoration: none;
  opacity: .001;
}
.dx-swatch-additional .dx-tabs {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  display: inline-block;
  width: 100%;
  text-align: center;
  table-layout: fixed;
}
.dx-swatch-additional .dx-tabs.dx-tabs-stretched {
  table-layout: auto;
}
.dx-swatch-additional .dx-tabs.dx-overflow-hidden {
  overflow: hidden;
}
.dx-swatch-additional .dx-tabs-wrapper {
  display: table-row;
}
.dx-swatch-additional .dx-tabs-scrollable .dx-tabs-wrapper {
  display: block;
  white-space: nowrap;
  height: 100%;
}
.dx-swatch-additional .dx-tabs-scrollable .dx-tab {
  height: 100%;
  display: inline-block;
}
.dx-swatch-additional .dx-tabs-scrollable .dx-tab::before {
  content: "";
  height: 100%;
  display: inline-block;
  vertical-align: middle;
}
.dx-swatch-additional .dx-tabs-scrollable .dx-scrollable-content {
  height: 100%;
}
.dx-swatch-additional .dx-tabs-nav-button {
  width: 25px;
  padding: 0;
  top: 0;
}
.dx-swatch-additional .dx-tabs-nav-button-left {
  left: 0;
}
.dx-swatch-additional .dx-tabs-nav-button-right {
  right: 0;
}
.dx-swatch-additional .dx-tabs-expanded {
  display: table;
}
.dx-swatch-additional .dx-tab {
  position: relative;
  display: table-cell;
  vertical-align: middle;
  cursor: pointer;
  white-space: nowrap;
}
.dx-swatch-additional .dx-tab a {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #fff;
  color: #fff;
  text-decoration: none;
  opacity: .001;
}
.dx-swatch-additional .dx-tab .dx-icon {
  display: block;
  -webkit-user-drag: none;
}
.dx-swatch-additional .dx-tab-content {
  display: inline-block;
  max-width: 100%;
}
.dx-swatch-additional .dx-tab-text {
  display: inline-block;
  margin: 0 auto;
  text-align: center;
  max-width: 100%;
  -webkit-user-drag: none;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.dx-swatch-additional .dx-tabs-item-badge {
  display: inline-block;
  vertical-align: top;
}
.dx-swatch-additional .dx-state-disabled .dx-tab {
  cursor: default;
}
.dx-swatch-additional .dx-scrollable-scrollbar-simulated {
  position: relative;
}
.dx-swatch-additional .dx-scrollable {
  display: block;
  height: 100%;
  min-height: 0;
}
.dx-swatch-additional .dx-scrollable:focus {
  outline: 0;
}
.dx-swatch-additional .dx-scrollable-native {
  -ms-overflow-style: -ms-autohiding-scrollbar;
  -ms-scroll-snap-type: proximity;
}
.dx-swatch-additional .dx-scrollable-native.dx-scrollable-wrapper > .dx-scrollable-container > .dx-scrollable-scrollbar {
  display: none;
}
.dx-swatch-additional .dx-scrollable-native.dx-scrollable-scrollbar-simulated .dx-scrollable-scrollbar {
  display: block;
}
.dx-swatch-additional .dx-scrollable-native > div.dx-scrollable-wrapper > .dx-scrollable-container,
.dx-swatch-additional .dx-scrollable-native.dx-scrollable-wrapper > .dx-scrollable-container {
  -webkit-overflow-scrolling: touch;
  position: relative;
  height: 100%;
}
.dx-swatch-additional .dx-scrollable-native.dx-scrollable-vertical,
.dx-swatch-additional .dx-scrollable-native.dx-scrollable-vertical > .dx-scrollable-wrapper > .dx-scrollable-container {
  -ms-touch-action: pan-y;
  touch-action: pan-y;
  overflow-x: hidden;
  overflow-y: auto;
}
.dx-swatch-additional .dx-scrollable-native.dx-scrollable-horizontal,
.dx-swatch-additional .dx-scrollable-native.dx-scrollable-horizontal > .dx-scrollable-wrapper > .dx-scrollable-container {
  -ms-touch-action: pan-x;
  touch-action: pan-x;
  float: none;
  overflow-x: auto;
  overflow-y: hidden;
}
.dx-swatch-additional .dx-scrollable-native.dx-scrollable-both,
.dx-swatch-additional .dx-scrollable-native.dx-scrollable-both > .dx-scrollable-wrapper > .dx-scrollable-container {
  -ms-touch-action: pan-y pan-x;
  touch-action: pan-y pan-x;
  float: none;
  overflow-x: auto;
  overflow-y: auto;
}
.dx-swatch-additional .dx-scrollable-native.dx-scrollable-disabled,
.dx-swatch-additional .dx-scrollable-native.dx-scrollable-disabled .dx-scrollable-container {
  -ms-touch-action: auto;
  touch-action: auto;
}
.dx-swatch-additional .dx-scrollable-native.dx-scrollable-scrollbars-hidden > .dx-scrollable-wrapper > .dx-scrollable-container {
  overflow: hidden;
}
.dx-swatch-additional .dx-scrollable-native.dx-scrollable-native-ios .dx-scrollable-content {
  min-height: 101%;
  -webkit-box-sizing: content-box;
  box-sizing: content-box;
}
.dx-swatch-additional .dx-scrollable-native.dx-scrollable-native-ios.dx-scrollable-horizontal .dx-scrollable-content {
  min-height: 0;
  padding: 0;
}
.dx-swatch-additional .dx-scrollable-native.dx-scrollable-native-generic {
  -ms-overflow-style: auto;
  overflow: hidden;
}
.dx-swatch-additional .dx-scrollable-native.dx-scrollable-native-generic .dx-scrollable-content {
  height: auto;
}
.dx-swatch-additional .dx-scrollable-native.dx-scrollable-native-android .dx-scrollable-content {
  -webkit-transform: none;
  transform: none;
  z-index: 0;
}
.dx-swatch-additional .dx-scrollable-scrollbar-simulated,
.dx-swatch-additional .dx-scrollable-scrollbar-simulated .dx-scrollable-container {
  scrollbar-width: none;
  -ms-overflow-style: none;
}
.dx-swatch-additional .dx-scrollable-scrollbar-simulated .dx-scrollable-container ::-webkit-scrollbar,
.dx-swatch-additional .dx-scrollable-scrollbar-simulated ::-webkit-scrollbar {
  display: none;
}
.dx-swatch-additional .dx-scrollable-container {
  -webkit-tap-highlight-color: transparent;
  overflow: hidden;
  width: 100%;
  height: 100%;
}
.dx-swatch-additional .dx-scrollable-container:focus {
  outline: 0;
}
.dx-swatch-additional .dx-scrollable-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
}
.dx-swatch-additional .dx-scrollable-content {
  position: relative;
  min-height: 100%;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
.dx-swatch-additional .dx-scrollable-content::after {
  display: block;
  content: "";
  clear: both;
}
.dx-swatch-additional .dx-scrollable-both .dx-scrollable-content,
.dx-swatch-additional .dx-scrollable-horizontal .dx-scrollable-content {
  display: block;
  float: left;
  min-width: 100%;
}
.dx-swatch-additional .dx-scrollable-scrollbar {
  position: absolute;
  pointer-events: auto;
}
.dx-swatch-additional .dx-scrollbar-vertical {
  top: 0;
  right: 0;
  height: 100%;
}
.dx-swatch-additional .dx-scrollbar-horizontal {
  bottom: 0;
  left: 0;
  width: 100%;
}
.dx-swatch-additional .dx-scrollable-scroll {
  position: relative;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-transform: translate(0,0);
  padding: 2px 0 2px 2px;
  background-color: transparent;
  opacity: 1;
  overflow: hidden;
  -webkit-transition: opacity linear;
  transition: opacity linear;
}
.dx-swatch-additional .dx-scrollable-scroll.dx-state-invisible {
  display: block!important;
  background-color: rgba(0,0,0,0);
  opacity: 0;
  -webkit-transition: opacity .5s linear 1s;
  transition: opacity .5s linear 1s;
}
.dx-swatch-additional .dx-rtl .dx-scrollable,
.dx-swatch-additional .dx-rtl .dx-scrollable .dx-scrollable-container,
.dx-swatch-additional .dx-rtl .dx-scrollable .dx-scrollable-content,
.dx-swatch-additional .dx-rtl.dx-scrollable,
.dx-swatch-additional .dx-rtl.dx-scrollable .dx-scrollable-container,
.dx-swatch-additional .dx-rtl.dx-scrollable .dx-scrollable-content {
  direction: ltr;
}
.dx-swatch-additional .dx-scrollable-native.dx-rtl .dx-scrollable .dx-scrollable-container,
.dx-swatch-additional .dx-scrollable-native.dx-rtl .dx-scrollable .dx-scrollable-content,
.dx-swatch-additional .dx-scrollable-native.dx-rtl.dx-scrollable .dx-scrollable-container,
.dx-swatch-additional .dx-scrollable-native.dx-rtl.dx-scrollable .dx-scrollable-content {
  direction: rtl;
}
.dx-swatch-additional .dx-scrollable-native.dx-rtl .dx-scrollable .dx-scrollable-content,
.dx-swatch-additional .dx-scrollable-native.dx-rtl.dx-scrollable .dx-scrollable-content {
  float: right;
}
.dx-swatch-additional .dx-rtl .dx-scrollable .dx-scrollable-content > *,
.dx-swatch-additional .dx-rtl.dx-scrollable .dx-scrollable-content > * {
  direction: rtl;
}
.dx-swatch-additional .dx-rtl .dx-scrollable .dx-scrollable-scrollbar.dx-scrollbar-vertical,
.dx-swatch-additional .dx-rtl.dx-scrollable .dx-scrollable-scrollbar.dx-scrollbar-vertical {
  right: auto;
  left: 0;
}
.dx-swatch-additional .dx-rtl .dx-scrollable .dx-scrollable-scrollbar.dx-scrollbar-horizontal,
.dx-swatch-additional .dx-rtl.dx-scrollable .dx-scrollable-scrollbar.dx-scrollbar-horizontal {
  direction: ltr;
}
.dx-swatch-additional .dx-scrollable-simulated .dx-scrollable-content {
  overflow-anchor: none;
}
.dx-swatch-additional .dx-scrollable-simulated.dx-scrollable-disabled .dx-scrollable-scrollbar {
  pointer-events: none;
}
.dx-swatch-additional .dx-scrollable-content {
  -webkit-transform: none;
}
.dx-swatch-additional .dx-rtl .dx-scrollable-scroll {
  padding-left: 0;
  padding-right: 2px;
}
.dx-swatch-additional .dx-scrollable-scroll-content {
  width: 100%;
  height: 100%;
  background-color: rgba(112,112,133,.7);
  -webkit-box-shadow: 0 0 0 1px transparent;
  box-shadow: 0 0 0 1px transparent;
}
.dx-swatch-additional .dx-scrollbar-hoverable {
  background-color: transparent;
}
.dx-swatch-additional .dx-scrollbar-hoverable .dx-scrollable-scroll.dx-state-invisible {
  opacity: 1;
}
.dx-swatch-additional .dx-scrollbar-hoverable .dx-scrollable-scroll.dx-state-invisible .dx-scrollable-scroll-content {
  background-color: rgba(0,0,0,0);
  -webkit-box-shadow: 0 0 0 1px transparent;
  box-shadow: 0 0 0 1px transparent;
}
.dx-swatch-additional .dx-scrollbar-vertical .dx-scrollable-scroll {
  float: right;
  width: 6px;
}
.dx-swatch-additional .dx-scrollbar-vertical.dx-scrollbar-hoverable {
  width: 6px;
  -webkit-transition: width .2s linear .15s,background-color .2s linear .15s;
  transition: width .2s linear .15s,background-color .2s linear .15s;
}
.dx-swatch-additional .dx-scrollbar-vertical.dx-scrollbar-hoverable .dx-scrollable-scroll {
  -webkit-transition: background-color .5s linear 1s,width .2s linear 150ms;
  transition: background-color .5s linear 1s,width .2s linear 150ms;
}
.dx-swatch-additional .dx-scrollbar-vertical.dx-scrollbar-hoverable .dx-scrollable-scroll .dx-scrollable-scroll-content {
  -webkit-transition: background-color .15s linear .15s,-webkit-box-shadow .15s linear .15s;
  transition: box-shadow .15s linear .15s,background-color .15s linear .15s,-webkit-box-shadow .15s linear .15s;
}
.dx-swatch-additional .dx-scrollbar-vertical.dx-scrollbar-hoverable .dx-scrollable-scroll.dx-state-invisible {
  -webkit-transition: background-color .5s linear 1s,width .2s linear .15s;
  transition: background-color .5s linear 1s,width .2s linear .15s;
}
.dx-swatch-additional .dx-scrollbar-vertical.dx-scrollbar-hoverable .dx-scrollable-scroll.dx-state-invisible .dx-scrollable-scroll-content {
  -webkit-transition: background-color .5s linear 1s,-webkit-box-shadow .5s linear 1s;
  transition: box-shadow .5s linear 1s,background-color .5s linear 1s,-webkit-box-shadow .5s linear 1s;
}
.dx-swatch-additional .dx-scrollbar-vertical.dx-scrollbar-hoverable.dx-scrollable-scrollbar-active,
.dx-swatch-additional .dx-scrollbar-vertical.dx-scrollbar-hoverable.dx-scrollable-scrollbar-active .dx-scrollable-scroll,
.dx-swatch-additional .dx-scrollbar-vertical.dx-scrollbar-hoverable.dx-state-hover,
.dx-swatch-additional .dx-scrollbar-vertical.dx-scrollbar-hoverable.dx-state-hover .dx-scrollable-scroll {
  width: 13px;
}
.dx-swatch-additional .dx-scrollbar-horizontal .dx-scrollable-scroll {
  height: 6px;
}
.dx-swatch-additional .dx-rtl .dx-scrollbar-horizontal .dx-scrollable-scroll,
.dx-swatch-additional .dx-scrollbar-horizontal .dx-scrollable-scroll {
  padding-left: 2px;
  padding-right: 2px;
  padding-bottom: 0;
}
.dx-swatch-additional .dx-scrollbar-horizontal.dx-scrollbar-hoverable {
  height: 6px;
  -webkit-transition: height .2s linear .15s,background-color .2s linear .15s;
  transition: height .2s linear .15s,background-color .2s linear .15s;
}
.dx-swatch-additional .dx-scrollbar-horizontal.dx-scrollbar-hoverable .dx-scrollable-scroll {
  -webkit-transition: background-color .5s linear 1s,height .2s linear .15s;
  transition: background-color .5s linear 1s,height .2s linear .15s;
}
.dx-swatch-additional .dx-scrollbar-horizontal.dx-scrollbar-hoverable .dx-scrollable-scroll .dx-scrollable-scroll-content {
  -webkit-transition: background-color .15s linear .15s,-webkit-box-shadow .15s linear .15s;
  transition: box-shadow .15s linear .15s,background-color .15s linear .15s,-webkit-box-shadow .15s linear .15s;
}
.dx-swatch-additional .dx-scrollbar-horizontal.dx-scrollbar-hoverable .dx-scrollable-scroll.dx-state-invisible {
  -webkit-transition: background-color .5s linear 1s,height .2s linear .15s;
  transition: background-color .5s linear 1s,height .2s linear .15s;
}
.dx-swatch-additional .dx-scrollbar-horizontal.dx-scrollbar-hoverable .dx-scrollable-scroll.dx-state-invisible .dx-scrollable-scroll-content {
  -webkit-transition: background-color .5s linear 1s,-webkit-box-shadow .5s linear 1s;
  transition: box-shadow .5s linear 1s,background-color .5s linear 1s,-webkit-box-shadow .5s linear 1s;
}
.dx-swatch-additional .dx-scrollbar-horizontal.dx-scrollbar-hoverable.dx-scrollable-scrollbar-active,
.dx-swatch-additional .dx-scrollbar-horizontal.dx-scrollbar-hoverable.dx-scrollable-scrollbar-active .dx-scrollable-scroll,
.dx-swatch-additional .dx-scrollbar-horizontal.dx-scrollbar-hoverable.dx-state-hover,
.dx-swatch-additional .dx-scrollbar-horizontal.dx-scrollbar-hoverable.dx-state-hover .dx-scrollable-scroll {
  height: 13px;
}
.dx-swatch-additional .dx-scrollable-scrollbars-alwaysvisible.dx-scrollable-both > .dx-scrollable-wrapper > .dx-scrollable-container > .dx-scrollable-content,
.dx-swatch-additional .dx-scrollable-scrollbars-alwaysvisible.dx-scrollable-vertical > .dx-scrollable-wrapper > .dx-scrollable-container > .dx-scrollable-content {
  padding-right: 6px;
}
.dx-swatch-additional .dx-rtl .dx-scrollable-scrollbars-alwaysvisible.dx-scrollable-both > .dx-scrollable-wrapper > .dx-scrollable-container > .dx-scrollable-content,
.dx-swatch-additional .dx-rtl .dx-scrollable-scrollbars-alwaysvisible.dx-scrollable-vertical > .dx-scrollable-wrapper > .dx-scrollable-container > .dx-scrollable-content,
.dx-swatch-additional .dx-scrollable-scrollbars-alwaysvisible.dx-scrollable-both.dx-rtl > .dx-scrollable-wrapper > .dx-scrollable-container > .dx-scrollable-content,
.dx-swatch-additional .dx-scrollable-scrollbars-alwaysvisible.dx-scrollable-vertical.dx-rtl > .dx-scrollable-wrapper > .dx-scrollable-container > .dx-scrollable-content {
  padding-right: 0;
  padding-left: 6px;
}
.dx-swatch-additional .dx-scrollable-scrollbars-alwaysvisible.dx-scrollable-both > .dx-scrollable-wrapper > .dx-scrollable-container > .dx-scrollable-content,
.dx-swatch-additional .dx-scrollable-scrollbars-alwaysvisible.dx-scrollable-horizontal > .dx-scrollable-wrapper > .dx-scrollable-container > .dx-scrollable-content {
  padding-bottom: 6px;
}
.dx-swatch-additional .dx-scrollview-pull-down-text,
.dx-swatch-additional .dx-scrollview-scrollbottom-text {
  margin-left: 10px;
  top: 18.5px;
  display: inline-block;
}
.dx-swatch-additional .dx-rtl .dx-scrollview-pull-down-text,
.dx-swatch-additional .dx-rtl .dx-scrollview-scrollbottom-text {
  margin-left: 0;
  margin-right: 10px;
}
.dx-swatch-additional .dx-scrollview-pull-down-text div {
  position: relative;
}
.dx-swatch-additional .dx-scrollview-pull-down-image {
  display: none;
}
.dx-swatch-additional .dx-scrollview-pull-down {
  text-align: center;
}
.dx-swatch-additional .dx-rtl .dx-scrollable .dx-scrollable-scroll,
.dx-swatch-additional .dx-rtl.dx-scrollable .dx-scrollable-scroll {
  float: left;
}
.dx-swatch-additional .dx-badge {
  padding: 2px 7px;
  border-radius: 14px;
  background-color: #ff5722;
  color: rgba(0,0,0,.87);
  font-size: 11px;
  margin-left: 4px;
  line-height: normal;
  margin-top: 1px;
}
.dx-swatch-additional .dx-rtl .dx-badge {
  margin-left: 0;
  margin-right: 4px;
}
.dx-swatch-additional .dx-tabs {
  padding: 0;
  background-color: #2d2d35;
  position: relative;
}
.dx-swatch-additional .dx-tabs .dx-inkripple {
  overflow: hidden;
}
.dx-swatch-additional .dx-tabs-nav-buttons .dx-tabs-scrollable {
  margin-right: 36px;
  margin-left: 36px;
}
.dx-swatch-additional .dx-tabs-nav-button {
  border: none;
  background-color: #2f2f38;
  position: absolute;
  height: 36px;
  border-radius: 0;
  -webkit-box-shadow: none;
  box-shadow: none;
}
.dx-swatch-additional .dx-tabs-nav-button.dx-button.dx-tabs-nav-button.dx-button-has-icon:not(.dx-button-has-text) {
  background-color: transparent;
  color: rgba(255,255,255,.54);
  -webkit-box-shadow: none;
  box-shadow: none;
  border-radius: 0;
}
.dx-swatch-additional .dx-tabs-nav-button.dx-button.dx-tabs-nav-button.dx-button-has-icon:not(.dx-button-has-text) .dx-icon {
  color: rgba(255,255,255,.54);
}
.dx-swatch-additional .dx-tabs-nav-button.dx-button.dx-tabs-nav-button.dx-button-has-icon:not(.dx-button-has-text).dx-state-focused,
.dx-swatch-additional .dx-tabs-nav-button.dx-button.dx-tabs-nav-button.dx-button-has-icon:not(.dx-button-has-text).dx-state-hover {
  background-color: rgba(255,255,255,.08);
  -webkit-box-shadow: none;
  box-shadow: none;
}
.dx-swatch-additional .dx-tabs-nav-button.dx-button.dx-tabs-nav-button.dx-button-has-icon:not(.dx-button-has-text).dx-state-active {
  background-color: rgba(255,255,255,.3);
  -webkit-box-shadow: none;
  box-shadow: none;
}
.dx-swatch-additional .dx-tabs-nav-button.dx-button.dx-tabs-nav-button.dx-button-has-icon:not(.dx-button-has-text).dx-state-disabled {
  background: 0 0;
  -webkit-box-shadow: none;
  box-shadow: none;
}
.dx-swatch-additional .dx-tabs-nav-button.dx-button.dx-tabs-nav-button.dx-button-has-icon:not(.dx-button-has-text).dx-state-disabled .dx-icon {
  opacity: .6;
}
.dx-swatch-additional .dx-tabs-nav-button.dx-button.dx-tabs-nav-button.dx-button-has-icon:not(.dx-button-has-text).dx-state-disabled .dx-button-text {
  color: rgba(255,255,255,.35);
}
.dx-swatch-additional .dx-tabs-nav-button.dx-button.dx-tabs-nav-button.dx-button-has-icon:not(.dx-button-has-text) .dx-inkripple-wave {
  background-color: rgba(255,255,255,.2);
}
.dx-swatch-additional .dx-tabs-nav-button .dx-button-content {
  padding: 0;
}
.dx-swatch-additional .dx-tabs-nav-button.dx-state-active {
  border: none;
}
.dx-swatch-additional .dx-tabs-nav-button.dx-state-disabled {
  opacity: 1;
  background-color: #2f2f38;
}
.dx-swatch-additional .dx-tabs-nav-button.dx-state-disabled .dx-button-content {
  opacity: 0;
}
.dx-swatch-additional .dx-tab {
  padding: 6px 12px;
  min-width: 82px;
  background-color: #2f2f38;
  color: rgba(255,255,255,.54);
}
.dx-swatch-additional .dx-tab.dx-state-hover {
  background-color: #464653;
}
.dx-swatch-additional .dx-tab .dx-icon {
  color: rgba(255,255,255,.54);
  vertical-align: middle;
  width: 18px;
  height: 18px;
  background-position: 0 0;
  background-size: 18px 18px;
  padding: 0;
  font-size: 18px;
  text-align: center;
  line-height: 18px;
  margin: 0 auto;
}
.dx-swatch-additional .dx-tab.dx-tab-selected,
.dx-swatch-additional .dx-tab.dx-tab-selected .dx-icon {
  color: #ff5722;
}
.dx-swatch-additional .dx-tab.dx-tab-selected::before {
  content: "";
  position: absolute;
  bottom: 0;
  height: 2px;
  left: 0;
  right: 0;
  background-color: #ff5722;
}
.dx-swatch-additional .dx-tab-content,
.dx-swatch-additional .dx-tab-text {
  vertical-align: middle;
  text-transform: uppercase;
  line-height: 24px;
  font-weight: 500;
}
.dx-swatch-additional .dx-state-disabled.dx-tabs {
  opacity: 1;
}
.dx-swatch-additional .dx-state-disabled .dx-tab-content {
  opacity: .3;
}
.dx-swatch-additional .dx-tabs.dx-navbar {
  margin: 0;
  width: 100%;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
}
.dx-swatch-additional .dx-tabs.dx-navbar .dx-icon {
  display: block;
  margin: 0 auto;
  width: 31px;
  height: 31px;
}
.dx-swatch-additional .dx-rtl .dx-tabs.dx-navbar .dx-icon,
.dx-swatch-additional .dx-rtl.dx-tabs.dx-navbar .dx-icon {
  margin: 0 auto;
}
.dx-swatch-additional .dx-tabs.dx-navbar .dx-tab-text {
  display: block;
  vertical-align: 50%;
}
.dx-swatch-additional .dx-nav-item {
  position: relative;
  vertical-align: bottom;
}
.dx-swatch-additional .dx-nav-item.dx-state-disabled {
  cursor: default;
}
.dx-swatch-additional .dx-nav-item-content {
  display: block;
}
.dx-swatch-additional .dx-nav-item a {
  display: block;
  height: 100%;
  text-decoration: none;
}
.dx-swatch-additional .dx-navbar-item-badge {
  position: absolute;
  right: 50%;
  margin-right: -26px;
  top: 11%;
}
.dx-swatch-additional .dx-rtl .dx-nav-item .dx-navbar-item-badge {
  right: auto;
  left: 50%;
  margin-right: auto;
  margin-left: -24px;
}
.dx-swatch-additional .dx-navbar {
  padding: 0;
  border: none;
}
.dx-swatch-additional .dx-nav-item,
.dx-swatch-additional .dx-rtl .dx-nav-item {
  background: #2f2f38;
}
.dx-swatch-additional .dx-nav-item .dx-tab-text,
.dx-swatch-additional .dx-rtl .dx-nav-item .dx-tab-text {
  line-height: normal;
  color: #fff;
}
.dx-swatch-additional .dx-navbar .dx-nav-item .dx-icon,
.dx-swatch-additional .dx-navbar .dx-rtl .dx-nav-item .dx-icon {
  width: 28px;
  height: 28px;
  background-position: 0 0;
  background-size: 28px 28px;
  padding: 0;
  font-size: 28px;
  text-align: center;
  line-height: 28px;
  color: #fff;
}
.dx-swatch-additional .dx-nav-item.dx-state-active::after,
.dx-swatch-additional .dx-nav-item.dx-state-focused::after,
.dx-swatch-additional .dx-nav-item.dx-tab-selected::after,
.dx-swatch-additional .dx-rtl .dx-nav-item.dx-state-active::after,
.dx-swatch-additional .dx-rtl .dx-nav-item.dx-state-focused::after,
.dx-swatch-additional .dx-rtl .dx-nav-item.dx-tab-selected::after {
  content: none;
}
.dx-swatch-additional .dx-nav-item.dx-tab-selected,
.dx-swatch-additional .dx-rtl .dx-nav-item.dx-tab-selected {
  background: #363640;
}
.dx-swatch-additional .dx-nav-item.dx-tab-selected .dx-icon,
.dx-swatch-additional .dx-nav-item.dx-tab-selected .dx-tab-text,
.dx-swatch-additional .dx-rtl .dx-nav-item.dx-tab-selected .dx-icon,
.dx-swatch-additional .dx-rtl .dx-nav-item.dx-tab-selected .dx-tab-text {
  color: #fff;
}
.dx-swatch-additional .dx-nav-item.dx-state-active,
.dx-swatch-additional .dx-rtl .dx-nav-item.dx-state-active {
  border: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
.dx-swatch-additional .dx-nav-item.dx-state-focused,
.dx-swatch-additional .dx-rtl .dx-nav-item.dx-state-focused {
  -webkit-box-shadow: inset 0 0 0 1px rgba(255,255,255,.05);
  box-shadow: inset 0 0 0 1px rgba(255,255,255,.05);
}
.dx-swatch-additional .dx-nav-item.dx-state-disabled .dx-icon,
.dx-swatch-additional .dx-rtl .dx-nav-item.dx-state-disabled .dx-icon {
  opacity: .5;
}
.dx-swatch-additional .dx-rtl .dx-navbar-item-badge {
  margin-left: -26px;
}
.dx-swatch-additional .dx-overlay-wrapper {
  top: 0;
  pointer-events: none;
  width: 100%;
  height: 100%;
  left: 0;
  z-index: 1000;
  color: #fff;
  font-weight: 400;
  font-size: 13px;
  font-family: Roboto,RobotoFallback,"Noto Kufi Arabic",Helvetica,Arial,sans-serif;
  line-height: 1.2857;
}
.dx-swatch-additional .dx-overlay-wrapper,
.dx-swatch-additional .dx-overlay-wrapper *,
.dx-swatch-additional .dx-overlay-wrapper ::after,
.dx-swatch-additional .dx-overlay-wrapper ::before,
.dx-swatch-additional .dx-overlay-wrapper::after,
.dx-swatch-additional .dx-overlay-wrapper::before {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
.dx-swatch-additional .dx-overlay-shader {
  pointer-events: auto;
  background-color: rgba(0,0,0,.61);
}
.dx-swatch-additional .dx-overlay-content {
  position: absolute;
  pointer-events: auto;
  z-index: 1000;
  outline: 0;
  overflow: hidden;
}
.dx-swatch-additional .dx-overlay-content > .dx-template-wrapper {
  height: 100%;
  width: 100%;
}
.dx-swatch-additional .dx-device-android .dx-overlay-content {
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
}
.dx-swatch-additional .dx-device-android .dx-scrollable-native .dx-overlay-content {
  -webkit-backface-visibility: visible;
  backface-visibility: visible;
}
.dx-swatch-additional .dx-overlay-wrapper input,
.dx-swatch-additional .dx-overlay-wrapper textarea {
  font-family: Roboto,RobotoFallback,"Noto Kufi Arabic",Helvetica,Arial,sans-serif;
  line-height: 1.2857;
}
.dx-swatch-additional .dx-validationsummary-item {
  color: #f44336;
}
.dx-swatch-additional .dx-invalid-message > .dx-overlay-content {
  background-color: transparent;
  color: #f44336;
  padding: 4px 0 0;
}
.dx-swatch-additional .dx-editor-filled .dx-invalid-message > .dx-overlay-content,
.dx-swatch-additional .dx-editor-outlined .dx-invalid-message > .dx-overlay-content {
  padding-left: 11px;
  padding-right: 11px;
}
.dx-swatch-additional .dx-validationsummary > .dx-validationsummary-item:not(:last-child) {
  margin-bottom: 6px;
}
.dx-swatch-additional .dx-form-validation-summary {
  margin-top: 20px;
}
.dx-swatch-additional .dx-searchbox .dx-icon-search {
  display: block;
  position: relative;
  pointer-events: none;
}
.dx-swatch-additional .dx-searchbox .dx-icon-search::before {
  display: inline-block;
  overflow: hidden;
  -webkit-box-sizing: content-box;
  box-sizing: content-box;
  position: static;
  text-indent: 0;
}
.dx-swatch-additional .dx-texteditor {
  display: block;
}
.dx-swatch-additional .dx-texteditor input::-ms-clear {
  display: none;
}
.dx-swatch-additional .dx-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  max-width: 100%;
  width: auto;
  height: 100%;
  text-align: left;
  cursor: text;
  pointer-events: none;
  color: gray;
  font-size: 13px;
}
.dx-swatch-additional .dx-placeholder::before {
  display: inline-block;
  vertical-align: middle;
  max-width: 100%;
  text-overflow: ellipsis;
  overflow: hidden;
  content: attr(DATA-DX_PLACEHOLDER);
  pointer-events: none;
  white-space: nowrap;
}
.dx-swatch-additional .dx-placeholder::after {
  content: " ";
  display: inline-block;
  height: 100%;
  vertical-align: middle;
}
.dx-swatch-additional .dx-texteditor-container {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  overflow: hidden;
  width: 100%;
  height: 100%;
}
.dx-swatch-additional .dx-texteditor-buttons-container {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  width: auto;
  -webkit-box-flex: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  -ms-flex-preferred-size: content;
  flex-basis: content;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}
.dx-swatch-additional .dx-texteditor-input-container {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  position: relative;
  -webkit-box-align: baseline;
  -ms-flex-align: baseline;
  align-items: baseline;
}
.dx-swatch-additional .dx-texteditor-input {
  -webkit-appearance: none;
  width: 100%;
  height: 100%;
  outline: 0;
  border: 0;
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
  margin: 0;
  background-color: transparent;
  color: #fff;
  font-size: 13px;
}
.dx-swatch-additional .dx-texteditor-input:-webkit-autofill + .dx-placeholder {
  display: none!important;
}
.dx-swatch-additional .dx-texteditor-input:autofill + .dx-placeholder {
  display: none!important;
}
.dx-swatch-additional .dx-texteditor-input:-moz-ui-invalid {
  box-shadow: none;
}
.dx-swatch-additional .dx-show-clear-button {
  position: relative;
}
.dx-swatch-additional .dx-clear-button-area {
  height: 100%;
  width: 34px;
  position: relative;
  cursor: pointer;
  text-align: justify;
}
.dx-swatch-additional .dx-clear-button-area .dx-icon-clear {
  position: absolute;
  display: inline-block;
  background-size: contain;
}
.dx-swatch-additional .dx-texteditor-empty .dx-clear-button-area {
  display: none;
}
.dx-swatch-additional .dx-state-disabled .dx-placeholder {
  cursor: auto;
}
.dx-swatch-additional .dx-state-disabled .dx-clear-button-area {
  display: none;
}
.dx-swatch-additional .dx-state-disabled .dx-texteditor-input {
  opacity: 1;
}
.dx-swatch-additional .dx-rtl .dx-texteditor .dx-placeholder,
.dx-swatch-additional .dx-rtl.dx-texteditor .dx-placeholder {
  text-align: right;
  left: auto;
  right: 0;
}
.dx-swatch-additional .dx-device-android .dx-texteditor-input {
  -webkit-user-modify: read-write-plaintext-only;
}
.dx-swatch-additional .dx-texteditor {
  border-top-left-radius: 2px;
  border-top-right-radius: 2px;
  position: relative;
}
.dx-swatch-additional .dx-texteditor::before {
  left: 0;
  right: 0;
  bottom: 0;
  height: 0;
  width: 100%;
  content: "";
  position: absolute;
  z-index: 2;
  -webkit-transform: scale(0);
  transform: scale(0);
}
.dx-swatch-additional .dx-texteditor::after {
  left: 0;
  right: 0;
  bottom: 0;
  height: 0;
  width: 100%;
  content: "";
  position: absolute;
}
.dx-swatch-additional .dx-texteditor.dx-state-focused .dx-texteditor-label,
.dx-swatch-additional .dx-texteditor.dx-state-focused.dx-state-hover .dx-texteditor-label {
  color: #ff5722;
  font-size: 11px;
}
.dx-swatch-additional .dx-texteditor.dx-state-focused.dx-invalid .dx-texteditor-label,
.dx-swatch-additional .dx-texteditor.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label {
  color: #f44336;
}
.dx-swatch-additional .dx-texteditor.dx-state-active,
.dx-swatch-additional .dx-texteditor.dx-state-focused {
  background-color: rgba(255,255,255,.04);
}
.dx-swatch-additional .dx-texteditor.dx-state-active::before,
.dx-swatch-additional .dx-texteditor.dx-state-focused::before {
  border-bottom: 2px solid #ff5722;
  -webkit-transform: scale(1);
  transform: scale(1);
  -webkit-transition: -webkit-transform .6s cubic-bezier(.4, 0, .02, 1);
  transition: transform .6s cubic-bezier(.4, 0, .02, 1);
  transition: transform .6s cubic-bezier(.4, 0, .02, 1),-webkit-transform .6s cubic-bezier(.4, 0, .02, 1);
}
.dx-swatch-additional .dx-texteditor.dx-invalid.dx-editor-underlined .dx-texteditor-input,
.dx-swatch-additional .dx-texteditor.dx-valid.dx-editor-underlined .dx-texteditor-input {
  padding-right: 30px;
}
.dx-swatch-additional .dx-texteditor.dx-invalid.dx-editor-underlined.dx-rtl .dx-texteditor-input,
.dx-swatch-additional .dx-texteditor.dx-valid.dx-editor-underlined.dx-rtl .dx-texteditor-input {
  padding-left: 30px;
  padding-right: 0;
}
.dx-swatch-additional .dx-texteditor.dx-invalid.dx-editor-filled .dx-texteditor-input,
.dx-swatch-additional .dx-texteditor.dx-invalid.dx-editor-outlined .dx-texteditor-input,
.dx-swatch-additional .dx-texteditor.dx-valid.dx-editor-filled .dx-texteditor-input,
.dx-swatch-additional .dx-texteditor.dx-valid.dx-editor-outlined .dx-texteditor-input {
  padding-right: 41px;
}
.dx-swatch-additional .dx-texteditor.dx-invalid.dx-editor-filled.dx-rtl .dx-texteditor-input,
.dx-swatch-additional .dx-texteditor.dx-invalid.dx-editor-outlined.dx-rtl .dx-texteditor-input,
.dx-swatch-additional .dx-texteditor.dx-valid.dx-editor-filled.dx-rtl .dx-texteditor-input,
.dx-swatch-additional .dx-texteditor.dx-valid.dx-editor-outlined.dx-rtl .dx-texteditor-input {
  padding: 9px 11px 8px 41px;
}
.dx-swatch-additional .dx-texteditor.dx-invalid.dx-editor-filled .dx-texteditor-input-container::after,
.dx-swatch-additional .dx-texteditor.dx-invalid.dx-editor-outlined .dx-texteditor-input-container::after,
.dx-swatch-additional .dx-texteditor.dx-valid.dx-editor-filled .dx-texteditor-input-container::after,
.dx-swatch-additional .dx-texteditor.dx-valid.dx-editor-outlined .dx-texteditor-input-container::after {
  right: 7px;
}
.dx-swatch-additional .dx-rtl .dx-texteditor.dx-invalid.dx-editor-filled .dx-texteditor-input-container::after,
.dx-swatch-additional .dx-rtl .dx-texteditor.dx-invalid.dx-editor-outlined .dx-texteditor-input-container::after,
.dx-swatch-additional .dx-rtl .dx-texteditor.dx-valid.dx-editor-filled .dx-texteditor-input-container::after,
.dx-swatch-additional .dx-rtl .dx-texteditor.dx-valid.dx-editor-outlined .dx-texteditor-input-container::after,
.dx-swatch-additional .dx-rtl.dx-texteditor.dx-invalid.dx-editor-filled .dx-texteditor-input-container::after,
.dx-swatch-additional .dx-rtl.dx-texteditor.dx-invalid.dx-editor-outlined .dx-texteditor-input-container::after,
.dx-swatch-additional .dx-rtl.dx-texteditor.dx-valid.dx-editor-filled .dx-texteditor-input-container::after,
.dx-swatch-additional .dx-rtl.dx-texteditor.dx-valid.dx-editor-outlined .dx-texteditor-input-container::after {
  left: 7px;
  right: auto;
}
.dx-swatch-additional .dx-texteditor.dx-invalid .dx-texteditor-input-container::after,
.dx-swatch-additional .dx-texteditor.dx-valid .dx-texteditor-input-container::after {
  right: 3.5px;
}
.dx-swatch-additional .dx-rtl .dx-texteditor.dx-invalid .dx-texteditor-input-container::after,
.dx-swatch-additional .dx-rtl .dx-texteditor.dx-valid .dx-texteditor-input-container::after,
.dx-swatch-additional .dx-rtl.dx-texteditor.dx-invalid .dx-texteditor-input-container::after,
.dx-swatch-additional .dx-rtl.dx-texteditor.dx-valid .dx-texteditor-input-container::after {
  left: 3.5px;
  right: auto;
}
.dx-swatch-additional .dx-texteditor.dx-validation-pending.dx-editor-underlined .dx-texteditor-input {
  padding-right: 30px;
}
.dx-swatch-additional .dx-texteditor.dx-validation-pending.dx-editor-underlined.dx-rtl .dx-texteditor-input {
  padding-left: 30px;
  padding-right: 0;
}
.dx-swatch-additional .dx-texteditor.dx-validation-pending.dx-editor-filled .dx-texteditor-input,
.dx-swatch-additional .dx-texteditor.dx-validation-pending.dx-editor-outlined .dx-texteditor-input {
  padding-right: 41px;
}
.dx-swatch-additional .dx-texteditor.dx-validation-pending.dx-editor-filled.dx-rtl .dx-texteditor-input,
.dx-swatch-additional .dx-texteditor.dx-validation-pending.dx-editor-outlined.dx-rtl .dx-texteditor-input {
  padding: 9px 11px 8px 41px;
}
.dx-swatch-additional .dx-texteditor.dx-validation-pending.dx-editor-filled .dx-texteditor-input-container .dx-pending-indicator,
.dx-swatch-additional .dx-texteditor.dx-validation-pending.dx-editor-outlined .dx-texteditor-input-container .dx-pending-indicator {
  right: 7px;
}
.dx-swatch-additional .dx-rtl .dx-texteditor.dx-validation-pending.dx-editor-filled .dx-texteditor-input-container .dx-pending-indicator,
.dx-swatch-additional .dx-rtl .dx-texteditor.dx-validation-pending.dx-editor-outlined .dx-texteditor-input-container .dx-pending-indicator,
.dx-swatch-additional .dx-rtl.dx-texteditor.dx-validation-pending.dx-editor-filled .dx-texteditor-input-container .dx-pending-indicator,
.dx-swatch-additional .dx-rtl.dx-texteditor.dx-validation-pending.dx-editor-outlined .dx-texteditor-input-container .dx-pending-indicator {
  left: 7px;
  right: auto;
}
.dx-swatch-additional .dx-texteditor.dx-validation-pending .dx-texteditor-input-container .dx-pending-indicator {
  right: 3.5px;
}
.dx-swatch-additional .dx-rtl .dx-texteditor.dx-validation-pending .dx-texteditor-input-container .dx-pending-indicator,
.dx-swatch-additional .dx-rtl.dx-texteditor.dx-validation-pending .dx-texteditor-input-container .dx-pending-indicator {
  left: 3.5px;
  right: auto;
}
.dx-swatch-additional .dx-texteditor.dx-editor-filled .dx-placeholder::before,
.dx-swatch-additional .dx-texteditor.dx-editor-filled .dx-texteditor-input,
.dx-swatch-additional .dx-texteditor.dx-editor-outlined .dx-placeholder::before,
.dx-swatch-additional .dx-texteditor.dx-editor-outlined .dx-texteditor-input {
  padding: 9px 11px 8px;
}
.dx-swatch-additional .dx-texteditor.dx-editor-filled {
  background-color: rgba(255,255,255,.04);
}
.dx-swatch-additional .dx-texteditor.dx-editor-filled::after {
  border-bottom: 1px solid rgba(255,255,255,.42);
}
.dx-swatch-additional .dx-texteditor.dx-editor-filled.dx-state-hover {
  background-color: rgba(255,255,255,.07);
}
.dx-swatch-additional .dx-texteditor.dx-editor-filled.dx-state-hover::after {
  border-bottom-color: #fff;
}
.dx-swatch-additional .dx-texteditor.dx-editor-filled.dx-state-disabled,
.dx-swatch-additional .dx-texteditor.dx-editor-filled.dx-state-readonly,
.dx-swatch-additional .dx-texteditor.dx-editor-filled.dx-state-readonly.dx-state-hover {
  background-color: rgba(255,255,255,.12);
}
.dx-swatch-additional .dx-texteditor.dx-editor-filled.dx-state-disabled .dx-texteditor-input,
.dx-swatch-additional .dx-texteditor.dx-editor-filled.dx-state-readonly .dx-texteditor-input,
.dx-swatch-additional .dx-texteditor.dx-editor-filled.dx-state-readonly.dx-state-hover .dx-texteditor-input {
  color: rgba(255,255,255,.5);
}
.dx-swatch-additional .dx-texteditor.dx-editor-underlined {
  background-color: transparent;
}
.dx-swatch-additional .dx-texteditor.dx-editor-underlined::after {
  border-bottom: 1px solid rgba(255,255,255,.42);
}
.dx-swatch-additional .dx-texteditor.dx-editor-underlined.dx-state-hover::after {
  border-bottom: 2px solid #fff;
}
.dx-swatch-additional .dx-texteditor.dx-editor-underlined .dx-placeholder::before,
.dx-swatch-additional .dx-texteditor.dx-editor-underlined .dx-texteditor-input {
  padding: 6px 0 5px;
}
.dx-swatch-additional .dx-texteditor.dx-editor-underlined.dx-state-disabled::after,
.dx-swatch-additional .dx-texteditor.dx-editor-underlined.dx-state-readonly.dx-state-hover::after,
.dx-swatch-additional .dx-texteditor.dx-editor-underlined.dx-state-readonly::after {
  border-bottom-style: dotted;
  border-bottom-width: 1px;
}
.dx-swatch-additional .dx-texteditor.dx-editor-underlined.dx-invalid::after {
  border-bottom-color: rgba(244,67,54,.4);
}
.dx-swatch-additional .dx-texteditor.dx-editor-outlined {
  background-color: transparent;
  border-radius: 2px;
  -webkit-box-shadow: inset 0 0 0 1px rgba(255,255,255,.42);
  box-shadow: inset 0 0 0 1px rgba(255,255,255,.42);
}
.dx-swatch-additional .dx-texteditor.dx-editor-outlined::before {
  display: none;
}
.dx-swatch-additional .dx-texteditor.dx-editor-outlined.dx-state-hover {
  -webkit-box-shadow: inset 0 0 0 1px #fff;
  box-shadow: inset 0 0 0 1px #fff;
}
.dx-swatch-additional .dx-texteditor.dx-editor-outlined.dx-state-disabled,
.dx-swatch-additional .dx-texteditor.dx-editor-outlined.dx-state-readonly,
.dx-swatch-additional .dx-texteditor.dx-editor-outlined.dx-state-readonly.dx-state-hover {
  -webkit-box-shadow: inset 0 0 0 1px rgba(255,255,255,.5);
  box-shadow: inset 0 0 0 1px rgba(255,255,255,.5);
}
.dx-swatch-additional .dx-texteditor.dx-editor-outlined.dx-state-disabled .dx-texteditor-input,
.dx-swatch-additional .dx-texteditor.dx-editor-outlined.dx-state-readonly .dx-texteditor-input,
.dx-swatch-additional .dx-texteditor.dx-editor-outlined.dx-state-readonly.dx-state-hover .dx-texteditor-input {
  color: rgba(255,255,255,.5);
}
.dx-swatch-additional .dx-texteditor.dx-editor-outlined.dx-state-focused {
  -webkit-box-shadow: inset 0 0 0 2px #ff5722;
  box-shadow: inset 0 0 0 2px #ff5722;
}
.dx-swatch-additional .dx-texteditor.dx-editor-outlined.dx-state-focused.dx-invalid {
  -webkit-box-shadow: inset 0 0 0 2px #f44336;
  box-shadow: inset 0 0 0 2px #f44336;
}
.dx-swatch-additional .dx-texteditor.dx-editor-outlined.dx-invalid {
  -webkit-box-shadow: inset 0 0 0 1px rgba(244,67,54,.4);
  box-shadow: inset 0 0 0 1px rgba(244,67,54,.4);
}
.dx-swatch-additional .dx-show-clear-button .dx-clear-button-area {
  width: 16px;
  min-width: 16px;
  right: 0;
}
.dx-swatch-additional .dx-show-clear-button .dx-icon-clear {
  color: #3d3d3d;
  background-color: #a8a8a8;
  border-radius: 50%;
  position: absolute;
  top: 50%;
  margin-top: -8px;
  width: 16px;
  height: 16px;
  background-position: 0 0;
  background-size: 16px 16px;
  padding: 0;
  font-size: 10px;
  text-align: center;
  line-height: 10px;
}
.dx-swatch-additional .dx-show-clear-button .dx-icon-clear::before {
  position: absolute;
  display: block;
  width: 10px;
  top: 50%;
  margin-top: -5px;
  left: 50%;
  margin-left: -5px;
}
.dx-swatch-additional .dx-show-clear-button:not(.dx-texteditor-empty).dx-invalid.dx-editor-underlined .dx-texteditor-input,
.dx-swatch-additional .dx-show-clear-button:not(.dx-texteditor-empty).dx-valid.dx-editor-underlined .dx-texteditor-input {
  padding-right: 30px;
}
.dx-swatch-additional .dx-show-clear-button:not(.dx-texteditor-empty).dx-invalid.dx-editor-underlined.dx-rtl .dx-texteditor-input,
.dx-swatch-additional .dx-show-clear-button:not(.dx-texteditor-empty).dx-valid.dx-editor-underlined.dx-rtl .dx-texteditor-input {
  padding-left: 30px;
  padding-right: 0;
}
.dx-swatch-additional .dx-show-clear-button:not(.dx-texteditor-empty).dx-invalid.dx-editor-filled .dx-texteditor-input,
.dx-swatch-additional .dx-show-clear-button:not(.dx-texteditor-empty).dx-invalid.dx-editor-outlined .dx-texteditor-input,
.dx-swatch-additional .dx-show-clear-button:not(.dx-texteditor-empty).dx-valid.dx-editor-filled .dx-texteditor-input,
.dx-swatch-additional .dx-show-clear-button:not(.dx-texteditor-empty).dx-valid.dx-editor-outlined .dx-texteditor-input {
  padding-right: 41px;
}
.dx-swatch-additional .dx-show-clear-button:not(.dx-texteditor-empty).dx-invalid.dx-editor-filled.dx-rtl .dx-texteditor-input,
.dx-swatch-additional .dx-show-clear-button:not(.dx-texteditor-empty).dx-invalid.dx-editor-outlined.dx-rtl .dx-texteditor-input,
.dx-swatch-additional .dx-show-clear-button:not(.dx-texteditor-empty).dx-valid.dx-editor-filled.dx-rtl .dx-texteditor-input,
.dx-swatch-additional .dx-show-clear-button:not(.dx-texteditor-empty).dx-valid.dx-editor-outlined.dx-rtl .dx-texteditor-input {
  padding: 9px 11px 8px 41px;
}
.dx-swatch-additional .dx-show-clear-button:not(.dx-texteditor-empty).dx-invalid.dx-editor-filled .dx-texteditor-input-container::after,
.dx-swatch-additional .dx-show-clear-button:not(.dx-texteditor-empty).dx-invalid.dx-editor-outlined .dx-texteditor-input-container::after,
.dx-swatch-additional .dx-show-clear-button:not(.dx-texteditor-empty).dx-valid.dx-editor-filled .dx-texteditor-input-container::after,
.dx-swatch-additional .dx-show-clear-button:not(.dx-texteditor-empty).dx-valid.dx-editor-outlined .dx-texteditor-input-container::after {
  right: 7px;
}
.dx-swatch-additional .dx-rtl .dx-show-clear-button:not(.dx-texteditor-empty).dx-invalid.dx-editor-filled .dx-texteditor-input-container::after,
.dx-swatch-additional .dx-rtl .dx-show-clear-button:not(.dx-texteditor-empty).dx-invalid.dx-editor-outlined .dx-texteditor-input-container::after,
.dx-swatch-additional .dx-rtl .dx-show-clear-button:not(.dx-texteditor-empty).dx-valid.dx-editor-filled .dx-texteditor-input-container::after,
.dx-swatch-additional .dx-rtl .dx-show-clear-button:not(.dx-texteditor-empty).dx-valid.dx-editor-outlined .dx-texteditor-input-container::after,
.dx-swatch-additional .dx-rtl.dx-show-clear-button:not(.dx-texteditor-empty).dx-invalid.dx-editor-filled .dx-texteditor-input-container::after,
.dx-swatch-additional .dx-rtl.dx-show-clear-button:not(.dx-texteditor-empty).dx-invalid.dx-editor-outlined .dx-texteditor-input-container::after,
.dx-swatch-additional .dx-rtl.dx-show-clear-button:not(.dx-texteditor-empty).dx-valid.dx-editor-filled .dx-texteditor-input-container::after,
.dx-swatch-additional .dx-rtl.dx-show-clear-button:not(.dx-texteditor-empty).dx-valid.dx-editor-outlined .dx-texteditor-input-container::after {
  left: 7px;
  right: auto;
}
.dx-swatch-additional .dx-show-clear-button:not(.dx-texteditor-empty).dx-invalid .dx-texteditor-input-container::after,
.dx-swatch-additional .dx-show-clear-button:not(.dx-texteditor-empty).dx-valid .dx-texteditor-input-container::after {
  right: 3.5px;
}
.dx-swatch-additional .dx-rtl .dx-show-clear-button:not(.dx-texteditor-empty).dx-invalid .dx-texteditor-input-container::after,
.dx-swatch-additional .dx-rtl .dx-show-clear-button:not(.dx-texteditor-empty).dx-valid .dx-texteditor-input-container::after,
.dx-swatch-additional .dx-rtl.dx-show-clear-button:not(.dx-texteditor-empty).dx-invalid .dx-texteditor-input-container::after,
.dx-swatch-additional .dx-rtl.dx-show-clear-button:not(.dx-texteditor-empty).dx-valid .dx-texteditor-input-container::after {
  left: 3.5px;
  right: auto;
}
.dx-swatch-additional .dx-show-clear-button:not(.dx-texteditor-empty).dx-validation-pending.dx-editor-underlined .dx-texteditor-input {
  padding-right: 30px;
}
.dx-swatch-additional .dx-show-clear-button:not(.dx-texteditor-empty).dx-validation-pending.dx-editor-underlined.dx-rtl .dx-texteditor-input {
  padding-left: 30px;
  padding-right: 0;
}
.dx-swatch-additional .dx-show-clear-button:not(.dx-texteditor-empty).dx-validation-pending.dx-editor-filled .dx-texteditor-input,
.dx-swatch-additional .dx-show-clear-button:not(.dx-texteditor-empty).dx-validation-pending.dx-editor-outlined .dx-texteditor-input {
  padding-right: 41px;
}
.dx-swatch-additional .dx-show-clear-button:not(.dx-texteditor-empty).dx-validation-pending.dx-editor-filled.dx-rtl .dx-texteditor-input,
.dx-swatch-additional .dx-show-clear-button:not(.dx-texteditor-empty).dx-validation-pending.dx-editor-outlined.dx-rtl .dx-texteditor-input {
  padding: 9px 11px 8px 41px;
}
.dx-swatch-additional .dx-show-clear-button:not(.dx-texteditor-empty).dx-validation-pending.dx-editor-filled .dx-texteditor-input-container .dx-pending-indicator,
.dx-swatch-additional .dx-show-clear-button:not(.dx-texteditor-empty).dx-validation-pending.dx-editor-outlined .dx-texteditor-input-container .dx-pending-indicator {
  right: 7px;
}
.dx-swatch-additional .dx-rtl .dx-show-clear-button:not(.dx-texteditor-empty).dx-validation-pending.dx-editor-filled .dx-texteditor-input-container .dx-pending-indicator,
.dx-swatch-additional .dx-rtl .dx-show-clear-button:not(.dx-texteditor-empty).dx-validation-pending.dx-editor-outlined .dx-texteditor-input-container .dx-pending-indicator,
.dx-swatch-additional .dx-rtl.dx-show-clear-button:not(.dx-texteditor-empty).dx-validation-pending.dx-editor-filled .dx-texteditor-input-container .dx-pending-indicator,
.dx-swatch-additional .dx-rtl.dx-show-clear-button:not(.dx-texteditor-empty).dx-validation-pending.dx-editor-outlined .dx-texteditor-input-container .dx-pending-indicator {
  left: 7px;
  right: auto;
}
.dx-swatch-additional .dx-show-clear-button:not(.dx-texteditor-empty).dx-validation-pending .dx-texteditor-input-container .dx-pending-indicator {
  right: 3.5px;
}
.dx-swatch-additional .dx-rtl .dx-show-clear-button:not(.dx-texteditor-empty).dx-validation-pending .dx-texteditor-input-container .dx-pending-indicator,
.dx-swatch-additional .dx-rtl.dx-show-clear-button:not(.dx-texteditor-empty).dx-validation-pending .dx-texteditor-input-container .dx-pending-indicator {
  left: 3.5px;
  right: auto;
}
.dx-swatch-additional .dx-invalid.dx-texteditor.dx-state-hover::after {
  border-bottom-color: rgba(244,67,54,.4);
}
.dx-swatch-additional .dx-invalid.dx-texteditor.dx-state-active::before,
.dx-swatch-additional .dx-invalid.dx-texteditor.dx-state-focused::before {
  border-top: 1px solid #f44336;
  border-bottom: 1px solid #f44336;
}
.dx-swatch-additional .dx-invalid.dx-texteditor.dx-show-invalid-badge .dx-texteditor-input-container::after {
  pointer-events: none;
  position: absolute;
  top: 50%;
  margin-top: -8px;
  width: 16px;
  height: 16px;
  text-align: center;
  line-height: 17px;
  font-size: 13px;
  font-weight: 500;
  background-color: #f44336;
  color: rgba(0,0,0,.87);
  content: "!";
  border-radius: 50%;
}
.dx-swatch-additional .dx-editor-filled .dx-texteditor-buttons-container > .dx-button,
.dx-swatch-additional .dx-editor-outlined .dx-texteditor-buttons-container > .dx-button {
  margin-left: 5px;
  margin-right: 5px;
}
.dx-swatch-additional .dx-editor-filled .dx-texteditor-buttons-container:first-child > .dx-button:first-child,
.dx-swatch-additional .dx-editor-outlined .dx-texteditor-buttons-container:first-child > .dx-button:first-child {
  margin-left: 10px;
}
.dx-swatch-additional .dx-editor-filled .dx-texteditor-buttons-container:first-child > .dx-button:last-child,
.dx-swatch-additional .dx-editor-outlined .dx-texteditor-buttons-container:first-child > .dx-button:last-child {
  margin-right: 0;
}
.dx-swatch-additional .dx-editor-filled .dx-texteditor-buttons-container:last-child > .dx-button:first-child,
.dx-swatch-additional .dx-editor-outlined .dx-texteditor-buttons-container:last-child > .dx-button:first-child {
  margin-left: 0;
}
.dx-swatch-additional .dx-editor-filled .dx-texteditor-buttons-container:last-child > .dx-button:last-child,
.dx-swatch-additional .dx-editor-outlined .dx-texteditor-buttons-container:last-child > .dx-button:last-child {
  margin-right: 10px;
}
.dx-swatch-additional .dx-editor-filled .dx-texteditor-buttons-container:last-child > .dx-dropdowneditor-button:last-child,
.dx-swatch-additional .dx-editor-filled .dx-texteditor-buttons-container:last-child > .dx-numberbox-spin-container:last-child,
.dx-swatch-additional .dx-editor-outlined .dx-texteditor-buttons-container:last-child > .dx-dropdowneditor-button:last-child,
.dx-swatch-additional .dx-editor-outlined .dx-texteditor-buttons-container:last-child > .dx-numberbox-spin-container:last-child {
  margin-right: 3px;
}
.dx-swatch-additional .dx-editor-filled .dx-texteditor-buttons-container:last-child > .dx-clear-button-area + div:empty:last-child,
.dx-swatch-additional .dx-editor-filled .dx-texteditor-buttons-container:last-child > .dx-clear-button-area:last-child,
.dx-swatch-additional .dx-editor-outlined .dx-texteditor-buttons-container:last-child > .dx-clear-button-area + div:empty:last-child,
.dx-swatch-additional .dx-editor-outlined .dx-texteditor-buttons-container:last-child > .dx-clear-button-area:last-child {
  margin-right: 7px;
}
.dx-swatch-additional .dx-rtl.dx-editor-filled .dx-texteditor-buttons-container:first-child > .dx-button:first-child,
.dx-swatch-additional .dx-rtl.dx-editor-outlined .dx-texteditor-buttons-container:first-child > .dx-button:first-child {
  margin-left: 5px;
  margin-right: 10px;
}
.dx-swatch-additional .dx-rtl.dx-editor-filled .dx-texteditor-buttons-container:first-child > .dx-button:last-child,
.dx-swatch-additional .dx-rtl.dx-editor-outlined .dx-texteditor-buttons-container:first-child > .dx-button:last-child {
  margin-left: 0;
  margin-right: 5px;
}
.dx-swatch-additional .dx-rtl.dx-editor-filled .dx-texteditor-buttons-container:first-child > .dx-button:first-child:last-child,
.dx-swatch-additional .dx-rtl.dx-editor-outlined .dx-texteditor-buttons-container:first-child > .dx-button:first-child:last-child {
  margin-left: 0;
  margin-right: 10px;
}
.dx-swatch-additional .dx-rtl.dx-editor-filled .dx-texteditor-buttons-container:last-child > .dx-button:first-child,
.dx-swatch-additional .dx-rtl.dx-editor-outlined .dx-texteditor-buttons-container:last-child > .dx-button:first-child {
  margin-left: 5px;
  margin-right: 0;
}
.dx-swatch-additional .dx-rtl.dx-editor-filled .dx-texteditor-buttons-container:last-child > .dx-button:last-child,
.dx-swatch-additional .dx-rtl.dx-editor-outlined .dx-texteditor-buttons-container:last-child > .dx-button:last-child {
  margin-left: 10px;
  margin-right: 5px;
}
.dx-swatch-additional .dx-rtl.dx-editor-filled .dx-texteditor-buttons-container:last-child > .dx-dropdowneditor-button:last-child,
.dx-swatch-additional .dx-rtl.dx-editor-filled .dx-texteditor-buttons-container:last-child > .dx-numberbox-spin-container:last-child,
.dx-swatch-additional .dx-rtl.dx-editor-outlined .dx-texteditor-buttons-container:last-child > .dx-dropdowneditor-button:last-child,
.dx-swatch-additional .dx-rtl.dx-editor-outlined .dx-texteditor-buttons-container:last-child > .dx-numberbox-spin-container:last-child {
  margin-left: 3px;
  margin-right: 0;
}
.dx-swatch-additional .dx-rtl.dx-editor-filled .dx-texteditor-buttons-container:last-child > .dx-clear-button-area + div:empty:last-child,
.dx-swatch-additional .dx-rtl.dx-editor-filled .dx-texteditor-buttons-container:last-child > .dx-clear-button-area:last-child,
.dx-swatch-additional .dx-rtl.dx-editor-outlined .dx-texteditor-buttons-container:last-child > .dx-clear-button-area + div:empty:last-child,
.dx-swatch-additional .dx-rtl.dx-editor-outlined .dx-texteditor-buttons-container:last-child > .dx-clear-button-area:last-child {
  margin-left: 7px;
  margin-right: 0;
}
.dx-swatch-additional .dx-texteditor-label {
  position: absolute;
  font-size: 11px;
  color: gray;
  white-space: nowrap;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  pointer-events: none;
  cursor: text;
  width: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  top: 0;
  left: 0;
}
.dx-swatch-additional .dx-texteditor-label .dx-label {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}
.dx-swatch-additional .dx-texteditor-label .dx-label span {
  overflow: hidden;
  text-overflow: ellipsis;
  width: auto;
  max-width: 100%;
  display: block;
}
.dx-swatch-additional .dx-invalid .dx-texteditor-label {
  color: #f44336;
}
.dx-swatch-additional .dx-rtl .dx-texteditor-label {
  left: auto;
  right: 0;
}
.dx-swatch-additional .dx-editor-filled .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-filled .dx-texteditor-label .dx-label-before {
  display: block;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  min-width: 11px;
}
.dx-swatch-additional .dx-editor-filled.dx-texteditor-with-floating-label,
.dx-swatch-additional .dx-editor-filled.dx-texteditor-with-label {
  position: relative;
}
.dx-swatch-additional .dx-editor-filled.dx-texteditor-with-floating-label .dx-texteditor-input,
.dx-swatch-additional .dx-editor-filled.dx-texteditor-with-floating-label.dx-lookup .dx-lookup-field,
.dx-swatch-additional .dx-editor-filled.dx-texteditor-with-label .dx-texteditor-input,
.dx-swatch-additional .dx-editor-filled.dx-texteditor-with-label.dx-lookup .dx-lookup-field {
  padding-top: 15px;
  padding-bottom: 2px;
}
.dx-swatch-additional .dx-editor-filled.dx-texteditor-with-floating-label.dx-textarea .dx-texteditor-container,
.dx-swatch-additional .dx-editor-filled.dx-texteditor-with-label.dx-textarea .dx-texteditor-container {
  padding-top: 15px;
}
.dx-swatch-additional .dx-editor-filled.dx-texteditor-with-floating-label.dx-textarea .dx-placeholder::before,
.dx-swatch-additional .dx-editor-filled.dx-texteditor-with-floating-label.dx-textarea .dx-texteditor-input,
.dx-swatch-additional .dx-editor-filled.dx-texteditor-with-label.dx-textarea .dx-placeholder::before,
.dx-swatch-additional .dx-editor-filled.dx-texteditor-with-label.dx-textarea .dx-texteditor-input {
  padding-top: 0;
}
.dx-swatch-additional .dx-editor-filled.dx-texteditor-with-floating-label.dx-textarea .dx-texteditor-label,
.dx-swatch-additional .dx-editor-filled.dx-texteditor-with-label.dx-textarea .dx-texteditor-label {
  top: 4px;
}
.dx-swatch-additional .dx-editor-filled.dx-texteditor-with-floating-label .dx-texteditor-label,
.dx-swatch-additional .dx-editor-filled.dx-texteditor-with-label .dx-texteditor-label {
  font-size: 11px;
  height: 11px;
  line-height: 11px;
  top: 4px;
  margin-top: 0;
}
.dx-swatch-additional .dx-editor-filled.dx-texteditor-with-floating-label .dx-placeholder::before,
.dx-swatch-additional .dx-editor-filled.dx-texteditor-with-label .dx-placeholder::before {
  padding-top: 15px;
}
.dx-swatch-additional .dx-editor-filled.dx-texteditor-with-floating-label.dx-lookup-empty .dx-placeholder,
.dx-swatch-additional .dx-editor-filled.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly .dx-placeholder,
.dx-swatch-additional .dx-editor-filled.dx-texteditor-with-floating-label.dx-texteditor-empty .dx-placeholder,
.dx-swatch-additional .dx-editor-filled.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly .dx-placeholder {
  display: none;
}
.dx-swatch-additional .dx-editor-filled.dx-texteditor-with-floating-label.dx-lookup-empty .dx-lookup-field,
.dx-swatch-additional .dx-editor-filled.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly .dx-lookup-field,
.dx-swatch-additional .dx-editor-filled.dx-texteditor-with-floating-label.dx-texteditor-empty .dx-lookup-field,
.dx-swatch-additional .dx-editor-filled.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly .dx-lookup-field {
  font-size: 0;
}
.dx-swatch-additional .dx-editor-filled.dx-texteditor-with-floating-label.dx-lookup-empty .dx-texteditor-label,
.dx-swatch-additional .dx-editor-filled.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly .dx-texteditor-label,
.dx-swatch-additional .dx-editor-filled.dx-texteditor-with-floating-label.dx-texteditor-empty .dx-texteditor-label,
.dx-swatch-additional .dx-editor-filled.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly .dx-texteditor-label {
  -webkit-transition: font-size .2s cubic-bezier(0, 0, .2, 1),top .2s cubic-bezier(0, 0, .2, 1),-webkit-transform .2s cubic-bezier(0, 0, .2, 1);
  transition: transform .2s cubic-bezier(0, 0, .2, 1),font-size .2s cubic-bezier(0, 0, .2, 1),top .2s cubic-bezier(0, 0, .2, 1),-webkit-transform .2s cubic-bezier(0, 0, .2, 1);
  font-size: 13px;
  top: 50%;
  margin-top: -6.5px;
  height: 13px;
  line-height: 13px;
}
.dx-swatch-additional .dx-editor-filled.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-textarea .dx-texteditor-label,
.dx-swatch-additional .dx-editor-filled.dx-texteditor-with-floating-label.dx-lookup-empty.dx-textarea .dx-texteditor-label,
.dx-swatch-additional .dx-editor-filled.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-textarea .dx-texteditor-label,
.dx-swatch-additional .dx-editor-filled.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-textarea .dx-texteditor-label {
  top: 15px;
  margin-top: 0;
  font-size: 13px;
  line-height: 13px;
  height: 13px;
}
.dx-swatch-additional .dx-editor-filled.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-textarea .dx-placeholder,
.dx-swatch-additional .dx-editor-filled.dx-texteditor-with-floating-label.dx-lookup-empty.dx-textarea .dx-placeholder,
.dx-swatch-additional .dx-editor-filled.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-textarea .dx-placeholder,
.dx-swatch-additional .dx-editor-filled.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-textarea .dx-placeholder {
  display: none;
}
.dx-swatch-additional .dx-editor-filled.dx-texteditor-with-floating-label.dx-dropdowneditor-active .dx-placeholder,
.dx-swatch-additional .dx-editor-filled.dx-texteditor-with-floating-label.dx-state-focused .dx-placeholder,
.dx-swatch-additional .dx-editor-filled.dx-texteditor-with-floating-label.dx-textarea.dx-state-focused .dx-placeholder {
  display: block;
}
.dx-swatch-additional .dx-editor-filled.dx-texteditor-with-floating-label.dx-dropdowneditor-active .dx-lookup-field,
.dx-swatch-additional .dx-editor-filled.dx-texteditor-with-floating-label.dx-state-focused .dx-lookup-field,
.dx-swatch-additional .dx-editor-filled.dx-texteditor-with-floating-label.dx-textarea.dx-state-focused .dx-lookup-field {
  font-size: 13px;
}
.dx-swatch-additional .dx-editor-filled.dx-texteditor-with-floating-label.dx-dropdowneditor-active .dx-texteditor-label,
.dx-swatch-additional .dx-editor-filled.dx-texteditor-with-floating-label.dx-state-focused .dx-texteditor-label,
.dx-swatch-additional .dx-editor-filled.dx-texteditor-with-floating-label.dx-textarea.dx-state-focused .dx-texteditor-label {
  font-size: 11px;
  height: 11px;
  line-height: 11px;
  top: 4px;
  margin-top: 0;
}
.dx-swatch-additional .dx-editor-filled.dx-texteditor-with-floating-label.dx-dropdowneditor-active.dx-textarea .dx-texteditor-label,
.dx-swatch-additional .dx-editor-filled.dx-texteditor-with-floating-label.dx-state-focused.dx-textarea .dx-texteditor-label,
.dx-swatch-additional .dx-editor-filled.dx-texteditor-with-floating-label.dx-textarea.dx-state-focused.dx-textarea .dx-texteditor-label {
  top: 4px;
}
.dx-swatch-additional .dx-editor-outlined .dx-texteditor-label {
  top: 0;
  left: 0;
  right: 0;
}
.dx-swatch-additional .dx-editor-outlined .dx-texteditor-label .dx-label {
  padding: 0;
}
.dx-swatch-additional .dx-editor-outlined .dx-texteditor-label .dx-label span {
  position: relative;
  font-size: 13px;
  top: 0;
  margin-top: 0;
}
.dx-swatch-additional .dx-editor-outlined .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined .dx-texteditor-label .dx-label-before {
  display: block;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
.dx-swatch-additional .dx-editor-outlined .dx-texteditor-label .dx-label-before {
  border-radius: 2px 0 0 2px;
  min-width: 11px;
}
.dx-swatch-additional .dx-editor-outlined .dx-texteditor-label .dx-label-after {
  -webkit-box-flex: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  min-width: 11px;
  border-radius: 0 2px 2px 0;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty {
  outline: 0;
  -webkit-box-shadow: none;
  box-shadow: none;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty .dx-texteditor-label {
  height: 100%;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty .dx-texteditor-label .dx-label {
  padding: 0;
  height: 100%;
  -webkit-box-shadow: inset 0 -1px rgba(255,255,255,.42),inset 0 1px rgba(255,255,255,.42),inset 1px 0 transparent,inset -1px 0 transparent;
  box-shadow: inset 0 -1px rgba(255,255,255,.42),inset 0 1px rgba(255,255,255,.42),inset 1px 0 transparent,inset -1px 0 transparent;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty .dx-texteditor-label .dx-label span {
  -webkit-transition: font-size .2s cubic-bezier(0, 0, .2, 1),top .2s cubic-bezier(0, 0, .2, 1),-webkit-transform .2s cubic-bezier(0, 0, .2, 1);
  transition: transform .2s cubic-bezier(0, 0, .2, 1),font-size .2s cubic-bezier(0, 0, .2, 1),top .2s cubic-bezier(0, 0, .2, 1),-webkit-transform .2s cubic-bezier(0, 0, .2, 1);
  -webkit-transform: translate(0,0);
  transform: translate(0,0);
  font-size: 13px;
  top: 50%;
  margin-top: -6.5px;
  height: 13px;
  line-height: 13px;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty .dx-texteditor-label .dx-label-before {
  -webkit-box-shadow: inset 0 -1px rgba(255,255,255,.42),inset 0 1px rgba(255,255,255,.42),inset -1px 0 transparent,inset 1px 0 rgba(255,255,255,.42);
  box-shadow: inset 0 -1px rgba(255,255,255,.42),inset 0 1px rgba(255,255,255,.42),inset -1px 0 transparent,inset 1px 0 rgba(255,255,255,.42);
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty .dx-texteditor-label .dx-label-after {
  -webkit-box-shadow: inset 0 -1px rgba(255,255,255,.42),inset 0 1px rgba(255,255,255,.42),inset -1px 0 rgba(255,255,255,.42),inset 1px 0 transparent;
  box-shadow: inset 0 -1px rgba(255,255,255,.42),inset 0 1px rgba(255,255,255,.42),inset -1px 0 rgba(255,255,255,.42),inset 1px 0 transparent;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-textarea .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-textarea .dx-texteditor-label .dx-label span {
  top: 9px;
  margin-top: 0;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty .dx-placeholder,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty .dx-placeholder {
  display: none;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty .dx-lookup-field {
  font-size: 0;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty .dx-texteditor-label .dx-label-before {
  -webkit-box-shadow: inset 0 -1px rgba(255,255,255,.42),inset 0 1px rgba(255,255,255,.42),inset -1px 0 rgba(255,255,255,.42),inset 1px 0 transparent;
  box-shadow: inset 0 -1px rgba(255,255,255,.42),inset 0 1px rgba(255,255,255,.42),inset -1px 0 rgba(255,255,255,.42),inset 1px 0 transparent;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty .dx-texteditor-label .dx-label-after {
  -webkit-box-shadow: inset 0 -1px rgba(255,255,255,.42),inset 0 1px rgba(255,255,255,.42),inset -1px 0 transparent,inset 1px 0 rgba(255,255,255,.42);
  box-shadow: inset 0 -1px rgba(255,255,255,.42),inset 0 1px rgba(255,255,255,.42),inset -1px 0 transparent,inset 1px 0 rgba(255,255,255,.42);
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-invalid,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-invalid {
  outline: 0;
  -webkit-box-shadow: none;
  box-shadow: none;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-invalid .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-invalid .dx-texteditor-label {
  height: 100%;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-invalid .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-invalid .dx-texteditor-label .dx-label {
  padding: 0;
  height: 100%;
  -webkit-box-shadow: inset 0 -1px rgba(244,67,54,.4),inset 0 1px rgba(244,67,54,.4),inset 1px 0 transparent,inset -1px 0 transparent;
  box-shadow: inset 0 -1px rgba(244,67,54,.4),inset 0 1px rgba(244,67,54,.4),inset 1px 0 transparent,inset -1px 0 transparent;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-invalid .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-invalid .dx-texteditor-label .dx-label span {
  -webkit-transition: font-size .2s cubic-bezier(0, 0, .2, 1),top .2s cubic-bezier(0, 0, .2, 1),-webkit-transform .2s cubic-bezier(0, 0, .2, 1);
  transition: transform .2s cubic-bezier(0, 0, .2, 1),font-size .2s cubic-bezier(0, 0, .2, 1),top .2s cubic-bezier(0, 0, .2, 1),-webkit-transform .2s cubic-bezier(0, 0, .2, 1);
  -webkit-transform: translate(0,0);
  transform: translate(0,0);
  font-size: 13px;
  top: 50%;
  margin-top: -6.5px;
  height: 13px;
  line-height: 13px;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-invalid .dx-texteditor-label .dx-label-before {
  -webkit-box-shadow: inset 0 -1px rgba(244,67,54,.4),inset 0 1px rgba(244,67,54,.4),inset -1px 0 transparent,inset 1px 0 rgba(244,67,54,.4);
  box-shadow: inset 0 -1px rgba(244,67,54,.4),inset 0 1px rgba(244,67,54,.4),inset -1px 0 transparent,inset 1px 0 rgba(244,67,54,.4);
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-invalid .dx-texteditor-label .dx-label-after {
  -webkit-box-shadow: inset 0 -1px rgba(244,67,54,.4),inset 0 1px rgba(244,67,54,.4),inset -1px 0 rgba(244,67,54,.4),inset 1px 0 transparent;
  box-shadow: inset 0 -1px rgba(244,67,54,.4),inset 0 1px rgba(244,67,54,.4),inset -1px 0 rgba(244,67,54,.4),inset 1px 0 transparent;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-invalid.dx-textarea .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-invalid.dx-textarea .dx-texteditor-label .dx-label span {
  top: 9px;
  margin-top: 0;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-invalid .dx-placeholder,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-invalid .dx-placeholder {
  display: none;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-invalid .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-invalid .dx-lookup-field {
  font-size: 0;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-invalid .dx-texteditor-label .dx-label-before {
  -webkit-box-shadow: inset 0 -1px rgba(244,67,54,.4),inset 0 1px rgba(244,67,54,.4),inset -1px 0 rgba(244,67,54,.4),inset 1px 0 transparent;
  box-shadow: inset 0 -1px rgba(244,67,54,.4),inset 0 1px rgba(244,67,54,.4),inset -1px 0 rgba(244,67,54,.4),inset 1px 0 transparent;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-invalid .dx-texteditor-label .dx-label-after {
  -webkit-box-shadow: inset 0 -1px rgba(244,67,54,.4),inset 0 1px rgba(244,67,54,.4),inset -1px 0 transparent,inset 1px 0 rgba(244,67,54,.4);
  box-shadow: inset 0 -1px rgba(244,67,54,.4),inset 0 1px rgba(244,67,54,.4),inset -1px 0 transparent,inset 1px 0 rgba(244,67,54,.4);
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover {
  outline: 0;
  -webkit-box-shadow: none;
  box-shadow: none;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover .dx-texteditor-label {
  height: 100%;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover .dx-texteditor-label .dx-label {
  padding: 0;
  height: 100%;
  -webkit-box-shadow: inset 0 -1px rgba(255,255,255,.5),inset 0 1px rgba(255,255,255,.5),inset 1px 0 transparent,inset -1px 0 transparent;
  box-shadow: inset 0 -1px rgba(255,255,255,.5),inset 0 1px rgba(255,255,255,.5),inset 1px 0 transparent,inset -1px 0 transparent;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover .dx-texteditor-label .dx-label span {
  -webkit-transition: font-size .2s cubic-bezier(0, 0, .2, 1),top .2s cubic-bezier(0, 0, .2, 1),-webkit-transform .2s cubic-bezier(0, 0, .2, 1);
  transition: transform .2s cubic-bezier(0, 0, .2, 1),font-size .2s cubic-bezier(0, 0, .2, 1),top .2s cubic-bezier(0, 0, .2, 1),-webkit-transform .2s cubic-bezier(0, 0, .2, 1);
  -webkit-transform: translate(0,0);
  transform: translate(0,0);
  font-size: 13px;
  top: 50%;
  margin-top: -6.5px;
  height: 13px;
  line-height: 13px;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover .dx-texteditor-label .dx-label-before {
  -webkit-box-shadow: inset 0 -1px rgba(255,255,255,.5),inset 0 1px rgba(255,255,255,.5),inset -1px 0 transparent,inset 1px 0 rgba(255,255,255,.5);
  box-shadow: inset 0 -1px rgba(255,255,255,.5),inset 0 1px rgba(255,255,255,.5),inset -1px 0 transparent,inset 1px 0 rgba(255,255,255,.5);
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover .dx-texteditor-label .dx-label-after {
  -webkit-box-shadow: inset 0 -1px rgba(255,255,255,.5),inset 0 1px rgba(255,255,255,.5),inset -1px 0 rgba(255,255,255,.5),inset 1px 0 transparent;
  box-shadow: inset 0 -1px rgba(255,255,255,.5),inset 0 1px rgba(255,255,255,.5),inset -1px 0 rgba(255,255,255,.5),inset 1px 0 transparent;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-textarea .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-textarea .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-textarea .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-textarea .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-textarea .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-textarea .dx-texteditor-label .dx-label span {
  top: 9px;
  margin-top: 0;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled .dx-placeholder,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly .dx-placeholder,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover .dx-placeholder,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled .dx-placeholder,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly .dx-placeholder,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover .dx-placeholder {
  display: none;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover .dx-lookup-field {
  font-size: 0;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover .dx-texteditor-label .dx-label-before {
  -webkit-box-shadow: inset 0 -1px rgba(255,255,255,.5),inset 0 1px rgba(255,255,255,.5),inset -1px 0 rgba(255,255,255,.5),inset 1px 0 transparent;
  box-shadow: inset 0 -1px rgba(255,255,255,.5),inset 0 1px rgba(255,255,255,.5),inset -1px 0 rgba(255,255,255,.5),inset 1px 0 transparent;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover .dx-texteditor-label .dx-label-after {
  -webkit-box-shadow: inset 0 -1px rgba(255,255,255,.5),inset 0 1px rgba(255,255,255,.5),inset -1px 0 transparent,inset 1px 0 rgba(255,255,255,.5);
  box-shadow: inset 0 -1px rgba(255,255,255,.5),inset 0 1px rgba(255,255,255,.5),inset -1px 0 transparent,inset 1px 0 rgba(255,255,255,.5);
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-state-focused,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-state-focused.dx-state-hover,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-textarea.dx-state-focused,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-state-hover,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-focused,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-focused.dx-state-hover,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-state-focused,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-state-hover,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-state-hover,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-textarea.dx-state-focused,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-state-hover,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-state-focused,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-state-focused.dx-state-hover,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-textarea.dx-state-focused,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-state-hover,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-focused,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-focused.dx-state-hover,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-state-focused,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-state-hover,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-state-hover,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-textarea.dx-state-focused,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-state-hover {
  outline: 0;
  -webkit-box-shadow: none;
  box-shadow: none;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-state-focused .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-state-focused.dx-state-hover .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-textarea.dx-state-focused .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-state-hover .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-focused .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-focused.dx-state-hover .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-state-focused .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-state-hover .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-state-hover .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-textarea.dx-state-focused .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-state-hover .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-state-focused .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-state-focused.dx-state-hover .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-textarea.dx-state-focused .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-state-hover .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-focused .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-focused.dx-state-hover .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-state-focused .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-state-hover .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-state-hover .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-textarea.dx-state-focused .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-state-hover .dx-texteditor-label {
  height: 100%;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-state-focused .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-textarea.dx-state-focused .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-focused .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-state-focused .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-textarea.dx-state-focused .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-state-focused .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-textarea.dx-state-focused .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-focused .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-state-focused .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-textarea.dx-state-focused .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label {
  padding: 0;
  height: 100%;
  -webkit-box-shadow: inset 0 -2px #ff5722,inset 0 2px #ff5722,inset 2px 0 transparent,inset -2px 0 transparent;
  box-shadow: inset 0 -2px #ff5722,inset 0 2px #ff5722,inset 2px 0 transparent,inset -2px 0 transparent;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-state-focused .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-textarea.dx-state-focused .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-focused .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-state-focused .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-textarea.dx-state-focused .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-state-focused .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-textarea.dx-state-focused .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-focused .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-state-focused .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-textarea.dx-state-focused .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label span {
  -webkit-transition: font-size .2s cubic-bezier(0, 0, .2, 1),top .2s cubic-bezier(0, 0, .2, 1),-webkit-transform .2s cubic-bezier(0, 0, .2, 1);
  transition: transform .2s cubic-bezier(0, 0, .2, 1),font-size .2s cubic-bezier(0, 0, .2, 1),top .2s cubic-bezier(0, 0, .2, 1),-webkit-transform .2s cubic-bezier(0, 0, .2, 1);
  -webkit-transform: translate(0,0);
  transform: translate(0,0);
  font-size: 13px;
  top: 50%;
  margin-top: -6.5px;
  height: 13px;
  line-height: 13px;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-state-focused .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-textarea.dx-state-focused .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-focused .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-state-focused .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-textarea.dx-state-focused .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-state-focused .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-textarea.dx-state-focused .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-focused .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-state-focused .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-textarea.dx-state-focused .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-before {
  -webkit-box-shadow: inset 0 -2px #ff5722,inset 0 2px #ff5722,inset -2px 0 transparent,inset 2px 0 #ff5722;
  box-shadow: inset 0 -2px #ff5722,inset 0 2px #ff5722,inset -2px 0 transparent,inset 2px 0 #ff5722;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-state-focused .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-textarea.dx-state-focused .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-focused .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-state-focused .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-textarea.dx-state-focused .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-state-focused .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-textarea.dx-state-focused .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-focused .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-state-focused .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-textarea.dx-state-focused .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-after {
  -webkit-box-shadow: inset 0 -2px #ff5722,inset 0 2px #ff5722,inset -2px 0 #ff5722,inset 2px 0 transparent;
  box-shadow: inset 0 -2px #ff5722,inset 0 2px #ff5722,inset -2px 0 #ff5722,inset 2px 0 transparent;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-state-focused.dx-state-hover.dx-textarea .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-state-focused.dx-textarea .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-state-hover.dx-textarea .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-textarea .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-focused.dx-state-hover.dx-textarea .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-focused.dx-textarea .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-state-hover.dx-textarea .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-textarea .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-state-hover.dx-textarea .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-textarea .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-state-hover.dx-textarea .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-textarea .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-state-focused.dx-state-hover.dx-textarea .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-state-focused.dx-textarea .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-state-hover.dx-textarea .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-textarea .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-focused.dx-state-hover.dx-textarea .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-focused.dx-textarea .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-state-hover.dx-textarea .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-textarea .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-state-hover.dx-textarea .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-textarea .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-state-hover.dx-textarea .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-textarea .dx-texteditor-label .dx-label span {
  top: 9px;
  margin-top: 0;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-state-focused .dx-placeholder,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-state-focused.dx-state-hover .dx-placeholder,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-textarea.dx-state-focused .dx-placeholder,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-state-hover .dx-placeholder,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-focused .dx-placeholder,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-focused.dx-state-hover .dx-placeholder,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-state-focused .dx-placeholder,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-state-hover .dx-placeholder,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused .dx-placeholder,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-state-hover .dx-placeholder,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-textarea.dx-state-focused .dx-placeholder,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-state-hover .dx-placeholder,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-state-focused .dx-placeholder,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-state-focused.dx-state-hover .dx-placeholder,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-textarea.dx-state-focused .dx-placeholder,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-state-hover .dx-placeholder,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-focused .dx-placeholder,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-focused.dx-state-hover .dx-placeholder,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-state-focused .dx-placeholder,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-state-hover .dx-placeholder,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused .dx-placeholder,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-state-hover .dx-placeholder,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-textarea.dx-state-focused .dx-placeholder,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-state-hover .dx-placeholder {
  display: none;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-state-focused .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-state-focused.dx-state-hover .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-textarea.dx-state-focused .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-state-hover .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-focused .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-focused.dx-state-hover .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-state-focused .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-state-hover .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-state-hover .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-textarea.dx-state-focused .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-state-hover .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-state-focused .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-state-focused.dx-state-hover .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-textarea.dx-state-focused .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-state-hover .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-focused .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-focused.dx-state-hover .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-state-focused .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-state-hover .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-state-hover .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-textarea.dx-state-focused .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-state-hover .dx-lookup-field {
  font-size: 0;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-state-focused.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-state-focused.dx-state-hover.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-state-hover.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-focused.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-focused.dx-state-hover.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-state-hover.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-state-hover.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-state-hover.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-state-focused.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-state-focused.dx-state-hover.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-state-hover.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-focused.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-focused.dx-state-hover.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-state-hover.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-state-hover.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-state-hover.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-state-focused .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-textarea.dx-state-focused .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-focused .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-state-focused .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-textarea.dx-state-focused .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-state-focused .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-textarea.dx-state-focused .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-focused .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-state-focused .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-textarea.dx-state-focused .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-before {
  -webkit-box-shadow: inset 0 -2px #ff5722,inset 0 2px #ff5722,inset -2px 0 #ff5722,inset 2px 0 transparent;
  box-shadow: inset 0 -2px #ff5722,inset 0 2px #ff5722,inset -2px 0 #ff5722,inset 2px 0 transparent;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-state-focused.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-state-focused.dx-state-hover.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-state-hover.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-focused.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-focused.dx-state-hover.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-state-hover.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-state-hover.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-state-hover.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-state-focused.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-state-focused.dx-state-hover.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-state-hover.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-focused.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-focused.dx-state-hover.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-state-hover.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-state-hover.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-state-hover.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-state-focused .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-textarea.dx-state-focused .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-focused .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-state-focused .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-textarea.dx-state-focused .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-state-focused .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-textarea.dx-state-focused .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-focused .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-state-focused .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-textarea.dx-state-focused .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-after {
  -webkit-box-shadow: inset 0 -2px #ff5722,inset 0 2px #ff5722,inset -2px 0 transparent,inset 2px 0 #ff5722;
  box-shadow: inset 0 -2px #ff5722,inset 0 2px #ff5722,inset -2px 0 transparent,inset 2px 0 #ff5722;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-state-focused.dx-invalid,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-state-focused.dx-state-hover.dx-invalid,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-invalid,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-focused.dx-invalid,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-focused.dx-state-hover.dx-invalid,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-invalid,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-state-hover.dx-invalid,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-invalid,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-invalid,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-state-focused.dx-invalid,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-state-focused.dx-state-hover.dx-invalid,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-invalid,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-focused.dx-invalid,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-focused.dx-state-hover.dx-invalid,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-invalid,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-state-hover.dx-invalid,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-invalid,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-invalid,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid {
  outline: 0;
  -webkit-box-shadow: none;
  box-shadow: none;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-state-focused.dx-invalid .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-invalid .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-focused.dx-invalid .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-invalid .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-invalid .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-invalid .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-state-focused.dx-invalid .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-invalid .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-focused.dx-invalid .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-invalid .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-invalid .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-invalid .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label {
  height: 100%;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label {
  padding: 0;
  height: 100%;
  -webkit-box-shadow: inset 0 -2px #f44336,inset 0 2px #f44336,inset 2px 0 transparent,inset -2px 0 transparent;
  box-shadow: inset 0 -2px #f44336,inset 0 2px #f44336,inset 2px 0 transparent,inset -2px 0 transparent;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label span {
  -webkit-transition: font-size .2s cubic-bezier(0, 0, .2, 1),top .2s cubic-bezier(0, 0, .2, 1),-webkit-transform .2s cubic-bezier(0, 0, .2, 1);
  transition: transform .2s cubic-bezier(0, 0, .2, 1),font-size .2s cubic-bezier(0, 0, .2, 1),top .2s cubic-bezier(0, 0, .2, 1),-webkit-transform .2s cubic-bezier(0, 0, .2, 1);
  -webkit-transform: translate(0,0);
  transform: translate(0,0);
  font-size: 13px;
  top: 50%;
  margin-top: -6.5px;
  height: 13px;
  line-height: 13px;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-before {
  -webkit-box-shadow: inset 0 -2px #f44336,inset 0 2px #f44336,inset -2px 0 transparent,inset 2px 0 #f44336;
  box-shadow: inset 0 -2px #f44336,inset 0 2px #f44336,inset -2px 0 transparent,inset 2px 0 #f44336;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-after {
  -webkit-box-shadow: inset 0 -2px #f44336,inset 0 2px #f44336,inset -2px 0 #f44336,inset 2px 0 transparent;
  box-shadow: inset 0 -2px #f44336,inset 0 2px #f44336,inset -2px 0 #f44336,inset 2px 0 transparent;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-state-focused.dx-invalid.dx-textarea .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-state-focused.dx-state-hover.dx-invalid.dx-textarea .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-invalid.dx-textarea .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid.dx-textarea .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-focused.dx-invalid.dx-textarea .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-focused.dx-state-hover.dx-invalid.dx-textarea .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-invalid.dx-textarea .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-state-hover.dx-invalid.dx-textarea .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-invalid.dx-textarea .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid.dx-textarea .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-invalid.dx-textarea .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid.dx-textarea .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-state-focused.dx-invalid.dx-textarea .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-state-focused.dx-state-hover.dx-invalid.dx-textarea .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-invalid.dx-textarea .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid.dx-textarea .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-focused.dx-invalid.dx-textarea .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-focused.dx-state-hover.dx-invalid.dx-textarea .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-invalid.dx-textarea .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-state-hover.dx-invalid.dx-textarea .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-invalid.dx-textarea .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid.dx-textarea .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-invalid.dx-textarea .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid.dx-textarea .dx-texteditor-label .dx-label span {
  top: 9px;
  margin-top: 0;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-state-focused.dx-invalid .dx-placeholder,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-state-focused.dx-state-hover.dx-invalid .dx-placeholder,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-invalid .dx-placeholder,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid .dx-placeholder,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-focused.dx-invalid .dx-placeholder,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-focused.dx-state-hover.dx-invalid .dx-placeholder,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-invalid .dx-placeholder,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-state-hover.dx-invalid .dx-placeholder,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-invalid .dx-placeholder,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid .dx-placeholder,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-invalid .dx-placeholder,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid .dx-placeholder,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-state-focused.dx-invalid .dx-placeholder,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-state-focused.dx-state-hover.dx-invalid .dx-placeholder,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-invalid .dx-placeholder,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid .dx-placeholder,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-focused.dx-invalid .dx-placeholder,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-focused.dx-state-hover.dx-invalid .dx-placeholder,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-invalid .dx-placeholder,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-state-hover.dx-invalid .dx-placeholder,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-invalid .dx-placeholder,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid .dx-placeholder,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-invalid .dx-placeholder,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid .dx-placeholder {
  display: none;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-state-focused.dx-invalid .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-state-focused.dx-state-hover.dx-invalid .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-invalid .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-focused.dx-invalid .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-focused.dx-state-hover.dx-invalid .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-invalid .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-state-hover.dx-invalid .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-invalid .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-invalid .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-state-focused.dx-invalid .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-state-focused.dx-state-hover.dx-invalid .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-invalid .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-focused.dx-invalid .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-focused.dx-state-hover.dx-invalid .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-invalid .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-state-hover.dx-invalid .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-invalid .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-invalid .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid .dx-lookup-field {
  font-size: 0;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-state-focused.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-state-focused.dx-state-hover.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-focused.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-focused.dx-state-hover.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-state-hover.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-state-focused.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-state-focused.dx-state-hover.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-focused.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-focused.dx-state-hover.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-state-hover.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-before {
  -webkit-box-shadow: inset 0 -2px #f44336,inset 0 2px #f44336,inset -2px 0 #f44336,inset 2px 0 transparent;
  box-shadow: inset 0 -2px #f44336,inset 0 2px #f44336,inset -2px 0 #f44336,inset 2px 0 transparent;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-state-focused.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-state-focused.dx-state-hover.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-focused.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-focused.dx-state-hover.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-state-hover.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-state-focused.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-state-focused.dx-state-hover.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-focused.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-focused.dx-state-hover.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-state-hover.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-disabled.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-state-hover.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-after {
  -webkit-box-shadow: inset 0 -2px #f44336,inset 0 2px #f44336,inset -2px 0 transparent,inset 2px 0 #f44336;
  box-shadow: inset 0 -2px #f44336,inset 0 2px #f44336,inset -2px 0 transparent,inset 2px 0 #f44336;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-hover,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-hover {
  outline: 0;
  -webkit-box-shadow: none;
  box-shadow: none;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-hover .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-hover .dx-texteditor-label {
  height: 100%;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-hover .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-hover .dx-texteditor-label .dx-label {
  padding: 0;
  height: 100%;
  -webkit-box-shadow: inset 0 -1px #fff,inset 0 1px #fff,inset 1px 0 transparent,inset -1px 0 transparent;
  box-shadow: inset 0 -1px #fff,inset 0 1px #fff,inset 1px 0 transparent,inset -1px 0 transparent;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-hover .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-hover .dx-texteditor-label .dx-label span {
  -webkit-transition: font-size .2s cubic-bezier(0, 0, .2, 1),top .2s cubic-bezier(0, 0, .2, 1),-webkit-transform .2s cubic-bezier(0, 0, .2, 1);
  transition: transform .2s cubic-bezier(0, 0, .2, 1),font-size .2s cubic-bezier(0, 0, .2, 1),top .2s cubic-bezier(0, 0, .2, 1),-webkit-transform .2s cubic-bezier(0, 0, .2, 1);
  -webkit-transform: translate(0,0);
  transform: translate(0,0);
  font-size: 13px;
  top: 50%;
  margin-top: -6.5px;
  height: 13px;
  line-height: 13px;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-hover .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-hover .dx-texteditor-label .dx-label-before {
  -webkit-box-shadow: inset 0 -1px #fff,inset 0 1px #fff,inset -1px 0 transparent,inset 1px 0 #fff;
  box-shadow: inset 0 -1px #fff,inset 0 1px #fff,inset -1px 0 transparent,inset 1px 0 #fff;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-hover .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-hover .dx-texteditor-label .dx-label-after {
  -webkit-box-shadow: inset 0 -1px #fff,inset 0 1px #fff,inset -1px 0 #fff,inset 1px 0 transparent;
  box-shadow: inset 0 -1px #fff,inset 0 1px #fff,inset -1px 0 #fff,inset 1px 0 transparent;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-hover.dx-textarea .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-hover.dx-textarea .dx-texteditor-label .dx-label span {
  top: 9px;
  margin-top: 0;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-hover .dx-placeholder,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-hover .dx-placeholder {
  display: none;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-hover .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-hover .dx-lookup-field {
  font-size: 0;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-hover.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-hover.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-hover .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-hover .dx-texteditor-label .dx-label-before {
  -webkit-box-shadow: inset 0 -1px #fff,inset 0 1px #fff,inset -1px 0 #fff,inset 1px 0 transparent;
  box-shadow: inset 0 -1px #fff,inset 0 1px #fff,inset -1px 0 #fff,inset 1px 0 transparent;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-hover.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-hover.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-hover .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-hover .dx-texteditor-label .dx-label-after {
  -webkit-box-shadow: inset 0 -1px #fff,inset 0 1px #fff,inset -1px 0 transparent,inset 1px 0 #fff;
  box-shadow: inset 0 -1px #fff,inset 0 1px #fff,inset -1px 0 transparent,inset 1px 0 #fff;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-hover.dx-dropdowneditor-active,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-hover.dx-dropdowneditor-active {
  outline: 0;
  -webkit-box-shadow: none;
  box-shadow: none;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-hover.dx-dropdowneditor-active .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-hover.dx-dropdowneditor-active .dx-texteditor-label {
  height: 100%;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-hover.dx-dropdowneditor-active .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-hover.dx-dropdowneditor-active .dx-texteditor-label .dx-label {
  height: 100%;
  -webkit-box-shadow: inset 0 -1px #fff,inset 0 1px transparent,inset 1px 0 transparent,inset -1px 0 transparent;
  box-shadow: inset 0 -1px #fff,inset 0 1px transparent,inset 1px 0 transparent,inset -1px 0 transparent;
  padding: 0 4px;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-hover.dx-dropdowneditor-active .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-hover.dx-dropdowneditor-active .dx-texteditor-label .dx-label span {
  -webkit-transform: translate(0,-5.5px);
  transform: translate(0,-5.5px);
  font-size: 11px;
  margin-top: 0;
  top: 0;
  height: 11px;
  line-height: 11px;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-hover.dx-dropdowneditor-active .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-hover.dx-dropdowneditor-active .dx-texteditor-label .dx-label-before {
  -webkit-box-shadow: inset 0 -1px #fff,inset 0 1px #fff,inset -1px 0 transparent,inset 1px 0 #fff;
  box-shadow: inset 0 -1px #fff,inset 0 1px #fff,inset -1px 0 transparent,inset 1px 0 #fff;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-hover.dx-dropdowneditor-active .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-hover.dx-dropdowneditor-active .dx-texteditor-label .dx-label-after {
  -webkit-box-shadow: inset 0 -1px #fff,inset 0 1px #fff,inset -1px 0 #fff,inset 1px 0 transparent;
  box-shadow: inset 0 -1px #fff,inset 0 1px #fff,inset -1px 0 #fff,inset 1px 0 transparent;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-hover.dx-dropdowneditor-active .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-hover.dx-dropdowneditor-active .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-hover.dx-dropdowneditor-active .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-hover.dx-dropdowneditor-active .dx-texteditor-label .dx-label-before {
  height: 100%;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-hover.dx-dropdowneditor-active .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-hover.dx-dropdowneditor-active .dx-lookup-field {
  font-size: 13px;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-hover.dx-dropdowneditor-active.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-hover.dx-dropdowneditor-active.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-hover.dx-dropdowneditor-active .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-hover.dx-dropdowneditor-active .dx-texteditor-label .dx-label-before {
  -webkit-box-shadow: inset 0 -1px #fff,inset 0 1px #fff,inset -1px 0 #fff,inset 1px 0 transparent;
  box-shadow: inset 0 -1px #fff,inset 0 1px #fff,inset -1px 0 #fff,inset 1px 0 transparent;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-hover.dx-dropdowneditor-active.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-hover.dx-dropdowneditor-active.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-hover.dx-dropdowneditor-active .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-hover.dx-dropdowneditor-active .dx-texteditor-label .dx-label-after {
  -webkit-box-shadow: inset 0 -1px #fff,inset 0 1px #fff,inset -1px 0 transparent,inset 1px 0 #fff;
  box-shadow: inset 0 -1px #fff,inset 0 1px #fff,inset -1px 0 transparent,inset 1px 0 #fff;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-hover.dx-invalid,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-hover.dx-invalid {
  outline: 0;
  -webkit-box-shadow: none;
  box-shadow: none;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-hover.dx-invalid .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-hover.dx-invalid .dx-texteditor-label {
  height: 100%;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label {
  padding: 0;
  height: 100%;
  -webkit-box-shadow: inset 0 -1px #f44336,inset 0 1px #f44336,inset 1px 0 transparent,inset -1px 0 transparent;
  box-shadow: inset 0 -1px #f44336,inset 0 1px #f44336,inset 1px 0 transparent,inset -1px 0 transparent;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label span {
  -webkit-transition: font-size .2s cubic-bezier(0, 0, .2, 1),top .2s cubic-bezier(0, 0, .2, 1),-webkit-transform .2s cubic-bezier(0, 0, .2, 1);
  transition: transform .2s cubic-bezier(0, 0, .2, 1),font-size .2s cubic-bezier(0, 0, .2, 1),top .2s cubic-bezier(0, 0, .2, 1),-webkit-transform .2s cubic-bezier(0, 0, .2, 1);
  -webkit-transform: translate(0,0);
  transform: translate(0,0);
  font-size: 13px;
  top: 50%;
  margin-top: -6.5px;
  height: 13px;
  line-height: 13px;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-before {
  -webkit-box-shadow: inset 0 -1px #f44336,inset 0 1px #f44336,inset -1px 0 transparent,inset 1px 0 #f44336;
  box-shadow: inset 0 -1px #f44336,inset 0 1px #f44336,inset -1px 0 transparent,inset 1px 0 #f44336;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-after {
  -webkit-box-shadow: inset 0 -1px #f44336,inset 0 1px #f44336,inset -1px 0 #f44336,inset 1px 0 transparent;
  box-shadow: inset 0 -1px #f44336,inset 0 1px #f44336,inset -1px 0 #f44336,inset 1px 0 transparent;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-hover.dx-invalid.dx-textarea .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-hover.dx-invalid.dx-textarea .dx-texteditor-label .dx-label span {
  top: 9px;
  margin-top: 0;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-hover.dx-invalid .dx-placeholder,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-hover.dx-invalid .dx-placeholder {
  display: none;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-hover.dx-invalid .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-hover.dx-invalid .dx-lookup-field {
  font-size: 0;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-hover.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-hover.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-before {
  -webkit-box-shadow: inset 0 -1px #f44336,inset 0 1px #f44336,inset -1px 0 #f44336,inset 1px 0 transparent;
  box-shadow: inset 0 -1px #f44336,inset 0 1px #f44336,inset -1px 0 #f44336,inset 1px 0 transparent;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-hover.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-hover.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-after {
  -webkit-box-shadow: inset 0 -1px #f44336,inset 0 1px #f44336,inset -1px 0 transparent,inset 1px 0 #f44336;
  box-shadow: inset 0 -1px #f44336,inset 0 1px #f44336,inset -1px 0 transparent,inset 1px 0 #f44336;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-dropdowneditor-active,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-dropdowneditor-active {
  outline: 0;
  -webkit-box-shadow: none;
  box-shadow: none;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-dropdowneditor-active .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-dropdowneditor-active .dx-texteditor-label {
  height: 100%;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-dropdowneditor-active .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-dropdowneditor-active .dx-texteditor-label .dx-label {
  height: 100%;
  -webkit-box-shadow: inset 0 -1px rgba(255,255,255,.42),inset 0 1px transparent,inset 1px 0 transparent,inset -1px 0 transparent;
  box-shadow: inset 0 -1px rgba(255,255,255,.42),inset 0 1px transparent,inset 1px 0 transparent,inset -1px 0 transparent;
  padding: 0 4px;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-dropdowneditor-active .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-dropdowneditor-active .dx-texteditor-label .dx-label span {
  -webkit-transform: translate(0,-5.5px);
  transform: translate(0,-5.5px);
  font-size: 11px;
  margin-top: 0;
  top: 0;
  height: 11px;
  line-height: 11px;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-dropdowneditor-active .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-dropdowneditor-active .dx-texteditor-label .dx-label-before {
  -webkit-box-shadow: inset 0 -1px rgba(255,255,255,.42),inset 0 1px rgba(255,255,255,.42),inset -1px 0 transparent,inset 1px 0 rgba(255,255,255,.42);
  box-shadow: inset 0 -1px rgba(255,255,255,.42),inset 0 1px rgba(255,255,255,.42),inset -1px 0 transparent,inset 1px 0 rgba(255,255,255,.42);
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-dropdowneditor-active .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-dropdowneditor-active .dx-texteditor-label .dx-label-after {
  -webkit-box-shadow: inset 0 -1px rgba(255,255,255,.42),inset 0 1px rgba(255,255,255,.42),inset -1px 0 rgba(255,255,255,.42),inset 1px 0 transparent;
  box-shadow: inset 0 -1px rgba(255,255,255,.42),inset 0 1px rgba(255,255,255,.42),inset -1px 0 rgba(255,255,255,.42),inset 1px 0 transparent;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-dropdowneditor-active .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-dropdowneditor-active .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-dropdowneditor-active .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-dropdowneditor-active .dx-texteditor-label .dx-label-before {
  height: 100%;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-dropdowneditor-active .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-dropdowneditor-active .dx-lookup-field {
  font-size: 13px;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-dropdowneditor-active.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-dropdowneditor-active.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-dropdowneditor-active .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-dropdowneditor-active .dx-texteditor-label .dx-label-before {
  -webkit-box-shadow: inset 0 -1px rgba(255,255,255,.42),inset 0 1px rgba(255,255,255,.42),inset -1px 0 rgba(255,255,255,.42),inset 1px 0 transparent;
  box-shadow: inset 0 -1px rgba(255,255,255,.42),inset 0 1px rgba(255,255,255,.42),inset -1px 0 rgba(255,255,255,.42),inset 1px 0 transparent;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-dropdowneditor-active.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-dropdowneditor-active.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-dropdowneditor-active .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-dropdowneditor-active .dx-texteditor-label .dx-label-after {
  -webkit-box-shadow: inset 0 -1px rgba(255,255,255,.42),inset 0 1px rgba(255,255,255,.42),inset -1px 0 transparent,inset 1px 0 rgba(255,255,255,.42);
  box-shadow: inset 0 -1px rgba(255,255,255,.42),inset 0 1px rgba(255,255,255,.42),inset -1px 0 transparent,inset 1px 0 rgba(255,255,255,.42);
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label {
  border: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  outline: 0;
  position: relative;
  margin-top: 5.5px;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-textarea .dx-texteditor-input-container,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-textarea .dx-texteditor-input-container {
  padding-top: 9px;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-textarea .dx-texteditor-input-container .dx-texteditor-input,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-textarea .dx-texteditor-input-container .dx-texteditor-input {
  padding-top: 0;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-disabled,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-readonly,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-readonly.dx-state-hover,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-disabled,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-readonly,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-readonly.dx-state-hover {
  outline: 0;
  -webkit-box-shadow: none;
  box-shadow: none;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-disabled .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-readonly .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-readonly.dx-state-hover .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-disabled .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-readonly .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-readonly.dx-state-hover .dx-texteditor-label {
  height: 100%;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-disabled .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-readonly .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-readonly.dx-state-hover .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-disabled .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-readonly .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-readonly.dx-state-hover .dx-texteditor-label .dx-label {
  height: 100%;
  -webkit-box-shadow: inset 0 -1px rgba(255,255,255,.5),inset 0 1px transparent,inset 1px 0 transparent,inset -1px 0 transparent;
  box-shadow: inset 0 -1px rgba(255,255,255,.5),inset 0 1px transparent,inset 1px 0 transparent,inset -1px 0 transparent;
  padding: 0 4px;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-disabled .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-readonly .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-readonly.dx-state-hover .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-disabled .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-readonly .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-readonly.dx-state-hover .dx-texteditor-label .dx-label span {
  -webkit-transform: translate(0,-5.5px);
  transform: translate(0,-5.5px);
  font-size: 11px;
  margin-top: 0;
  top: 0;
  height: 11px;
  line-height: 11px;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-disabled .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-readonly .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-readonly.dx-state-hover .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-disabled .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-readonly .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-readonly.dx-state-hover .dx-texteditor-label .dx-label-before {
  -webkit-box-shadow: inset 0 -1px rgba(255,255,255,.5),inset 0 1px rgba(255,255,255,.5),inset -1px 0 transparent,inset 1px 0 rgba(255,255,255,.5);
  box-shadow: inset 0 -1px rgba(255,255,255,.5),inset 0 1px rgba(255,255,255,.5),inset -1px 0 transparent,inset 1px 0 rgba(255,255,255,.5);
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-disabled .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-readonly .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-readonly.dx-state-hover .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-disabled .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-readonly .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-readonly.dx-state-hover .dx-texteditor-label .dx-label-after {
  -webkit-box-shadow: inset 0 -1px rgba(255,255,255,.5),inset 0 1px rgba(255,255,255,.5),inset -1px 0 rgba(255,255,255,.5),inset 1px 0 transparent;
  box-shadow: inset 0 -1px rgba(255,255,255,.5),inset 0 1px rgba(255,255,255,.5),inset -1px 0 rgba(255,255,255,.5),inset 1px 0 transparent;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-disabled .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-disabled .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-readonly .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-readonly .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-readonly.dx-state-hover .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-readonly.dx-state-hover .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-disabled .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-disabled .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-readonly .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-readonly .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-readonly.dx-state-hover .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-readonly.dx-state-hover .dx-texteditor-label .dx-label-before {
  height: 100%;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-disabled .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-readonly .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-readonly.dx-state-hover .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-disabled .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-readonly .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-readonly.dx-state-hover .dx-lookup-field {
  font-size: 13px;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-disabled.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-readonly.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-readonly.dx-state-hover.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-disabled.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-readonly.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-readonly.dx-state-hover.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-disabled .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-readonly .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-readonly.dx-state-hover .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-label.dx-state-disabled .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-label.dx-state-readonly .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-label.dx-state-readonly.dx-state-hover .dx-texteditor-label .dx-label-before {
  -webkit-box-shadow: inset 0 -1px rgba(255,255,255,.5),inset 0 1px rgba(255,255,255,.5),inset -1px 0 rgba(255,255,255,.5),inset 1px 0 transparent;
  box-shadow: inset 0 -1px rgba(255,255,255,.5),inset 0 1px rgba(255,255,255,.5),inset -1px 0 rgba(255,255,255,.5),inset 1px 0 transparent;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-disabled.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-readonly.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-readonly.dx-state-hover.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-disabled.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-readonly.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-readonly.dx-state-hover.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-disabled .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-readonly .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-readonly.dx-state-hover .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-label.dx-state-disabled .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-label.dx-state-readonly .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-label.dx-state-readonly.dx-state-hover .dx-texteditor-label .dx-label-after {
  -webkit-box-shadow: inset 0 -1px rgba(255,255,255,.5),inset 0 1px rgba(255,255,255,.5),inset -1px 0 transparent,inset 1px 0 rgba(255,255,255,.5);
  box-shadow: inset 0 -1px rgba(255,255,255,.5),inset 0 1px rgba(255,255,255,.5),inset -1px 0 transparent,inset 1px 0 rgba(255,255,255,.5);
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-invalid,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-invalid {
  outline: 0;
  -webkit-box-shadow: none;
  box-shadow: none;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-invalid .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-invalid .dx-texteditor-label {
  height: 100%;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-invalid .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-invalid .dx-texteditor-label .dx-label {
  height: 100%;
  -webkit-box-shadow: inset 0 -1px rgba(244,67,54,.4),inset 0 1px transparent,inset 1px 0 transparent,inset -1px 0 transparent;
  box-shadow: inset 0 -1px rgba(244,67,54,.4),inset 0 1px transparent,inset 1px 0 transparent,inset -1px 0 transparent;
  padding: 0 4px;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-invalid .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-invalid .dx-texteditor-label .dx-label span {
  -webkit-transform: translate(0,-5.5px);
  transform: translate(0,-5.5px);
  font-size: 11px;
  margin-top: 0;
  top: 0;
  height: 11px;
  line-height: 11px;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-invalid .dx-texteditor-label .dx-label-before {
  -webkit-box-shadow: inset 0 -1px rgba(244,67,54,.4),inset 0 1px rgba(244,67,54,.4),inset -1px 0 transparent,inset 1px 0 rgba(244,67,54,.4);
  box-shadow: inset 0 -1px rgba(244,67,54,.4),inset 0 1px rgba(244,67,54,.4),inset -1px 0 transparent,inset 1px 0 rgba(244,67,54,.4);
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-invalid .dx-texteditor-label .dx-label-after {
  -webkit-box-shadow: inset 0 -1px rgba(244,67,54,.4),inset 0 1px rgba(244,67,54,.4),inset -1px 0 rgba(244,67,54,.4),inset 1px 0 transparent;
  box-shadow: inset 0 -1px rgba(244,67,54,.4),inset 0 1px rgba(244,67,54,.4),inset -1px 0 rgba(244,67,54,.4),inset 1px 0 transparent;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-invalid .dx-texteditor-label .dx-label-before {
  height: 100%;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-invalid .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-invalid .dx-lookup-field {
  font-size: 13px;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-label.dx-invalid .dx-texteditor-label .dx-label-before {
  -webkit-box-shadow: inset 0 -1px rgba(244,67,54,.4),inset 0 1px rgba(244,67,54,.4),inset -1px 0 rgba(244,67,54,.4),inset 1px 0 transparent;
  box-shadow: inset 0 -1px rgba(244,67,54,.4),inset 0 1px rgba(244,67,54,.4),inset -1px 0 rgba(244,67,54,.4),inset 1px 0 transparent;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-label.dx-invalid .dx-texteditor-label .dx-label-after {
  -webkit-box-shadow: inset 0 -1px rgba(244,67,54,.4),inset 0 1px rgba(244,67,54,.4),inset -1px 0 transparent,inset 1px 0 rgba(244,67,54,.4);
  box-shadow: inset 0 -1px rgba(244,67,54,.4),inset 0 1px rgba(244,67,54,.4),inset -1px 0 transparent,inset 1px 0 rgba(244,67,54,.4);
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label .dx-texteditor-label {
  height: 100%;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label .dx-texteditor-label .dx-label {
  padding: 0 4px;
  -webkit-box-shadow: inset 0 -1px rgba(255,255,255,.42),inset 0 1px transparent,inset 1px 0 transparent,inset -1px 0 transparent;
  box-shadow: inset 0 -1px rgba(255,255,255,.42),inset 0 1px transparent,inset 1px 0 transparent,inset -1px 0 transparent;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label .dx-texteditor-label .dx-label span {
  -webkit-transform: translate(0,-5.5px);
  transform: translate(0,-5.5px);
  font-size: 11px;
  top: 0;
  margin-top: 0;
  height: 11px;
  line-height: 11px;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label .dx-texteditor-label .dx-label-before {
  -webkit-box-shadow: inset 0 -1px rgba(255,255,255,.42),inset 0 1px rgba(255,255,255,.42),inset -1px 0 transparent,inset 1px 0 rgba(255,255,255,.42);
  box-shadow: inset 0 -1px rgba(255,255,255,.42),inset 0 1px rgba(255,255,255,.42),inset -1px 0 transparent,inset 1px 0 rgba(255,255,255,.42);
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label .dx-texteditor-label .dx-label-after {
  -webkit-box-shadow: inset 0 -1px rgba(255,255,255,.42),inset 0 1px rgba(255,255,255,.42),inset -1px 0 rgba(255,255,255,.42),inset 1px 0 transparent;
  box-shadow: inset 0 -1px rgba(255,255,255,.42),inset 0 1px rgba(255,255,255,.42),inset -1px 0 rgba(255,255,255,.42),inset 1px 0 transparent;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-label .dx-texteditor-label .dx-label-before {
  border-radius: 0 2px 2px 0;
  -webkit-box-shadow: inset 0 -1px rgba(255,255,255,.42),inset 0 1px rgba(255,255,255,.42),inset -1px 0 rgba(255,255,255,.42),inset 1px 0 transparent;
  box-shadow: inset 0 -1px rgba(255,255,255,.42),inset 0 1px rgba(255,255,255,.42),inset -1px 0 rgba(255,255,255,.42),inset 1px 0 transparent;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-label .dx-texteditor-label .dx-label-after {
  border-radius: 2px 0 0 2px;
  -webkit-box-shadow: inset 0 -1px rgba(255,255,255,.42),inset 0 1px rgba(255,255,255,.42),inset -1px 0 transparent,inset 1px 0 rgba(255,255,255,.42);
  box-shadow: inset 0 -1px rgba(255,255,255,.42),inset 0 1px rgba(255,255,255,.42),inset -1px 0 transparent,inset 1px 0 rgba(255,255,255,.42);
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-hover,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-hover {
  outline: 0;
  -webkit-box-shadow: none;
  box-shadow: none;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-hover .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-hover .dx-texteditor-label {
  height: 100%;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-hover .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-hover .dx-texteditor-label .dx-label {
  height: 100%;
  -webkit-box-shadow: inset 0 -1px #fff,inset 0 1px transparent,inset 1px 0 transparent,inset -1px 0 transparent;
  box-shadow: inset 0 -1px #fff,inset 0 1px transparent,inset 1px 0 transparent,inset -1px 0 transparent;
  padding: 0 4px;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-hover .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-hover .dx-texteditor-label .dx-label span {
  -webkit-transform: translate(0,-5.5px);
  transform: translate(0,-5.5px);
  font-size: 11px;
  margin-top: 0;
  top: 0;
  height: 11px;
  line-height: 11px;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-hover .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-hover .dx-texteditor-label .dx-label-before {
  -webkit-box-shadow: inset 0 -1px #fff,inset 0 1px #fff,inset -1px 0 transparent,inset 1px 0 #fff;
  box-shadow: inset 0 -1px #fff,inset 0 1px #fff,inset -1px 0 transparent,inset 1px 0 #fff;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-hover .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-hover .dx-texteditor-label .dx-label-after {
  -webkit-box-shadow: inset 0 -1px #fff,inset 0 1px #fff,inset -1px 0 #fff,inset 1px 0 transparent;
  box-shadow: inset 0 -1px #fff,inset 0 1px #fff,inset -1px 0 #fff,inset 1px 0 transparent;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-hover .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-hover .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-hover .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-hover .dx-texteditor-label .dx-label-before {
  height: 100%;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-hover .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-hover .dx-lookup-field {
  font-size: 13px;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-hover.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-hover.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-hover .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-label.dx-state-hover .dx-texteditor-label .dx-label-before {
  -webkit-box-shadow: inset 0 -1px #fff,inset 0 1px #fff,inset -1px 0 #fff,inset 1px 0 transparent;
  box-shadow: inset 0 -1px #fff,inset 0 1px #fff,inset -1px 0 #fff,inset 1px 0 transparent;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-hover.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-hover.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-hover .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-label.dx-state-hover .dx-texteditor-label .dx-label-after {
  -webkit-box-shadow: inset 0 -1px #fff,inset 0 1px #fff,inset -1px 0 transparent,inset 1px 0 #fff;
  box-shadow: inset 0 -1px #fff,inset 0 1px #fff,inset -1px 0 transparent,inset 1px 0 #fff;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-hover.dx-invalid,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-hover.dx-invalid {
  outline: 0;
  -webkit-box-shadow: none;
  box-shadow: none;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-hover.dx-invalid .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-hover.dx-invalid .dx-texteditor-label {
  height: 100%;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label {
  height: 100%;
  -webkit-box-shadow: inset 0 -1px #f44336,inset 0 1px transparent,inset 1px 0 transparent,inset -1px 0 transparent;
  box-shadow: inset 0 -1px #f44336,inset 0 1px transparent,inset 1px 0 transparent,inset -1px 0 transparent;
  padding: 0 4px;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label span {
  -webkit-transform: translate(0,-5.5px);
  transform: translate(0,-5.5px);
  font-size: 11px;
  margin-top: 0;
  top: 0;
  height: 11px;
  line-height: 11px;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-before {
  -webkit-box-shadow: inset 0 -1px #f44336,inset 0 1px #f44336,inset -1px 0 transparent,inset 1px 0 #f44336;
  box-shadow: inset 0 -1px #f44336,inset 0 1px #f44336,inset -1px 0 transparent,inset 1px 0 #f44336;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-after {
  -webkit-box-shadow: inset 0 -1px #f44336,inset 0 1px #f44336,inset -1px 0 #f44336,inset 1px 0 transparent;
  box-shadow: inset 0 -1px #f44336,inset 0 1px #f44336,inset -1px 0 #f44336,inset 1px 0 transparent;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-before {
  height: 100%;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-hover.dx-invalid .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-hover.dx-invalid .dx-lookup-field {
  font-size: 13px;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-hover.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-hover.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-label.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-before {
  -webkit-box-shadow: inset 0 -1px #f44336,inset 0 1px #f44336,inset -1px 0 #f44336,inset 1px 0 transparent;
  box-shadow: inset 0 -1px #f44336,inset 0 1px #f44336,inset -1px 0 #f44336,inset 1px 0 transparent;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-hover.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-hover.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-label.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-after {
  -webkit-box-shadow: inset 0 -1px #f44336,inset 0 1px #f44336,inset -1px 0 transparent,inset 1px 0 #f44336;
  box-shadow: inset 0 -1px #f44336,inset 0 1px #f44336,inset -1px 0 transparent,inset 1px 0 #f44336;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-dropdowneditor-active.dx-state-focused,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-dropdowneditor-active.dx-state-focused.dx-state-hover,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-focused,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-focused.dx-state-hover,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-textarea.dx-state-focused,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-textarea.dx-state-focused.dx-state-hover,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-dropdowneditor-active.dx-state-focused,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-dropdowneditor-active.dx-state-focused.dx-state-hover,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-focused,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-focused.dx-state-hover,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-textarea.dx-state-focused,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-textarea.dx-state-focused.dx-state-hover {
  outline: 0;
  -webkit-box-shadow: none;
  box-shadow: none;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-dropdowneditor-active.dx-state-focused .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-dropdowneditor-active.dx-state-focused.dx-state-hover .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-focused .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-focused.dx-state-hover .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-textarea.dx-state-focused .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-textarea.dx-state-focused.dx-state-hover .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-dropdowneditor-active.dx-state-focused .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-dropdowneditor-active.dx-state-focused.dx-state-hover .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-focused .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-focused.dx-state-hover .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-textarea.dx-state-focused .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-textarea.dx-state-focused.dx-state-hover .dx-texteditor-label {
  height: 100%;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-dropdowneditor-active.dx-state-focused .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-dropdowneditor-active.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-focused .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-textarea.dx-state-focused .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-textarea.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-dropdowneditor-active.dx-state-focused .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-dropdowneditor-active.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-focused .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-textarea.dx-state-focused .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-textarea.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label {
  height: 100%;
  -webkit-box-shadow: inset 0 -2px #ff5722,inset 0 2px transparent,inset 2px 0 transparent,inset -2px 0 transparent;
  box-shadow: inset 0 -2px #ff5722,inset 0 2px transparent,inset 2px 0 transparent,inset -2px 0 transparent;
  padding: 0 4px;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-dropdowneditor-active.dx-state-focused .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-dropdowneditor-active.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-focused .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-textarea.dx-state-focused .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-textarea.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-dropdowneditor-active.dx-state-focused .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-dropdowneditor-active.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-focused .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-textarea.dx-state-focused .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-textarea.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label span {
  -webkit-transform: translate(0,-5.5px);
  transform: translate(0,-5.5px);
  font-size: 11px;
  margin-top: 0;
  top: 0;
  height: 11px;
  line-height: 11px;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-dropdowneditor-active.dx-state-focused .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-dropdowneditor-active.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-focused .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-textarea.dx-state-focused .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-textarea.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-dropdowneditor-active.dx-state-focused .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-dropdowneditor-active.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-focused .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-textarea.dx-state-focused .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-textarea.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-before {
  -webkit-box-shadow: inset 0 -2px #ff5722,inset 0 2px #ff5722,inset -2px 0 transparent,inset 2px 0 #ff5722;
  box-shadow: inset 0 -2px #ff5722,inset 0 2px #ff5722,inset -2px 0 transparent,inset 2px 0 #ff5722;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-dropdowneditor-active.dx-state-focused .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-dropdowneditor-active.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-focused .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-textarea.dx-state-focused .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-textarea.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-dropdowneditor-active.dx-state-focused .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-dropdowneditor-active.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-focused .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-textarea.dx-state-focused .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-textarea.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-after {
  -webkit-box-shadow: inset 0 -2px #ff5722,inset 0 2px #ff5722,inset -2px 0 #ff5722,inset 2px 0 transparent;
  box-shadow: inset 0 -2px #ff5722,inset 0 2px #ff5722,inset -2px 0 #ff5722,inset 2px 0 transparent;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-dropdowneditor-active.dx-state-focused .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-dropdowneditor-active.dx-state-focused .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-dropdowneditor-active.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-dropdowneditor-active.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-focused .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-focused .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-textarea.dx-state-focused .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-textarea.dx-state-focused .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-textarea.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-textarea.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-dropdowneditor-active.dx-state-focused .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-dropdowneditor-active.dx-state-focused .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-dropdowneditor-active.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-dropdowneditor-active.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-focused .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-focused .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-textarea.dx-state-focused .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-textarea.dx-state-focused .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-textarea.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-textarea.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-before {
  height: 100%;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-dropdowneditor-active.dx-state-focused .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-dropdowneditor-active.dx-state-focused.dx-state-hover .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-focused .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-focused.dx-state-hover .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-textarea.dx-state-focused .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-textarea.dx-state-focused.dx-state-hover .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-dropdowneditor-active.dx-state-focused .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-dropdowneditor-active.dx-state-focused.dx-state-hover .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-focused .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-focused.dx-state-hover .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-textarea.dx-state-focused .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-textarea.dx-state-focused.dx-state-hover .dx-lookup-field {
  font-size: 13px;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-dropdowneditor-active.dx-state-focused.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-dropdowneditor-active.dx-state-focused.dx-state-hover.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-focused.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-focused.dx-state-hover.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-textarea.dx-state-focused.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-textarea.dx-state-focused.dx-state-hover.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-dropdowneditor-active.dx-state-focused.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-dropdowneditor-active.dx-state-focused.dx-state-hover.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-focused.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-focused.dx-state-hover.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-textarea.dx-state-focused.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-textarea.dx-state-focused.dx-state-hover.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-dropdowneditor-active.dx-state-focused .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-dropdowneditor-active.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-focused .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-textarea.dx-state-focused .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-textarea.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-label.dx-dropdowneditor-active.dx-state-focused .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-label.dx-dropdowneditor-active.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-label.dx-state-focused .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-label.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-label.dx-textarea.dx-state-focused .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-label.dx-textarea.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-before {
  -webkit-box-shadow: inset 0 -2px #ff5722,inset 0 2px #ff5722,inset -2px 0 #ff5722,inset 2px 0 transparent;
  box-shadow: inset 0 -2px #ff5722,inset 0 2px #ff5722,inset -2px 0 #ff5722,inset 2px 0 transparent;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-dropdowneditor-active.dx-state-focused.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-dropdowneditor-active.dx-state-focused.dx-state-hover.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-focused.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-focused.dx-state-hover.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-textarea.dx-state-focused.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-textarea.dx-state-focused.dx-state-hover.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-dropdowneditor-active.dx-state-focused.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-dropdowneditor-active.dx-state-focused.dx-state-hover.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-focused.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-focused.dx-state-hover.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-textarea.dx-state-focused.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-textarea.dx-state-focused.dx-state-hover.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-dropdowneditor-active.dx-state-focused .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-dropdowneditor-active.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-focused .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-textarea.dx-state-focused .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-textarea.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-label.dx-dropdowneditor-active.dx-state-focused .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-label.dx-dropdowneditor-active.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-label.dx-state-focused .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-label.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-label.dx-textarea.dx-state-focused .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-label.dx-textarea.dx-state-focused.dx-state-hover .dx-texteditor-label .dx-label-after {
  -webkit-box-shadow: inset 0 -2px #ff5722,inset 0 2px #ff5722,inset -2px 0 transparent,inset 2px 0 #ff5722;
  box-shadow: inset 0 -2px #ff5722,inset 0 2px #ff5722,inset -2px 0 transparent,inset 2px 0 #ff5722;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-dropdowneditor-active.dx-state-focused.dx-invalid,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-dropdowneditor-active.dx-state-focused.dx-state-hover.dx-invalid,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-focused.dx-invalid,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-focused.dx-state-hover.dx-invalid,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-textarea.dx-state-focused.dx-invalid,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-dropdowneditor-active.dx-state-focused.dx-invalid,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-dropdowneditor-active.dx-state-focused.dx-state-hover.dx-invalid,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-focused.dx-invalid,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-focused.dx-state-hover.dx-invalid,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-textarea.dx-state-focused.dx-invalid,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid {
  outline: 0;
  -webkit-box-shadow: none;
  box-shadow: none;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-dropdowneditor-active.dx-state-focused.dx-invalid .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-dropdowneditor-active.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-focused.dx-invalid .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-textarea.dx-state-focused.dx-invalid .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-dropdowneditor-active.dx-state-focused.dx-invalid .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-dropdowneditor-active.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-focused.dx-invalid .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-textarea.dx-state-focused.dx-invalid .dx-texteditor-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label {
  height: 100%;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-dropdowneditor-active.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-dropdowneditor-active.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-textarea.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-dropdowneditor-active.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-dropdowneditor-active.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-textarea.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label {
  height: 100%;
  -webkit-box-shadow: inset 0 -2px #f44336,inset 0 2px transparent,inset 2px 0 transparent,inset -2px 0 transparent;
  box-shadow: inset 0 -2px #f44336,inset 0 2px transparent,inset 2px 0 transparent,inset -2px 0 transparent;
  padding: 0 4px;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-dropdowneditor-active.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-dropdowneditor-active.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-textarea.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-dropdowneditor-active.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-dropdowneditor-active.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-textarea.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label span,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label span {
  -webkit-transform: translate(0,-5.5px);
  transform: translate(0,-5.5px);
  font-size: 11px;
  margin-top: 0;
  top: 0;
  height: 11px;
  line-height: 11px;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-dropdowneditor-active.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-dropdowneditor-active.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-textarea.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-dropdowneditor-active.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-dropdowneditor-active.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-textarea.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-before {
  -webkit-box-shadow: inset 0 -2px #f44336,inset 0 2px #f44336,inset -2px 0 transparent,inset 2px 0 #f44336;
  box-shadow: inset 0 -2px #f44336,inset 0 2px #f44336,inset -2px 0 transparent,inset 2px 0 #f44336;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-dropdowneditor-active.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-dropdowneditor-active.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-textarea.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-dropdowneditor-active.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-dropdowneditor-active.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-textarea.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-after {
  -webkit-box-shadow: inset 0 -2px #f44336,inset 0 2px #f44336,inset -2px 0 #f44336,inset 2px 0 transparent;
  box-shadow: inset 0 -2px #f44336,inset 0 2px #f44336,inset -2px 0 #f44336,inset 2px 0 transparent;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-dropdowneditor-active.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-dropdowneditor-active.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-dropdowneditor-active.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-dropdowneditor-active.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-textarea.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-textarea.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-dropdowneditor-active.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-dropdowneditor-active.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-dropdowneditor-active.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-dropdowneditor-active.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-textarea.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-textarea.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-before {
  height: 100%;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-dropdowneditor-active.dx-state-focused.dx-invalid .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-dropdowneditor-active.dx-state-focused.dx-state-hover.dx-invalid .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-focused.dx-invalid .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-focused.dx-state-hover.dx-invalid .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-textarea.dx-state-focused.dx-invalid .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-dropdowneditor-active.dx-state-focused.dx-invalid .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-dropdowneditor-active.dx-state-focused.dx-state-hover.dx-invalid .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-focused.dx-invalid .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-focused.dx-state-hover.dx-invalid .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-textarea.dx-state-focused.dx-invalid .dx-lookup-field,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid .dx-lookup-field {
  font-size: 13px;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-dropdowneditor-active.dx-state-focused.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-dropdowneditor-active.dx-state-focused.dx-state-hover.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-focused.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-focused.dx-state-hover.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-textarea.dx-state-focused.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-dropdowneditor-active.dx-state-focused.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-dropdowneditor-active.dx-state-focused.dx-state-hover.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-focused.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-focused.dx-state-hover.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-textarea.dx-state-focused.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-dropdowneditor-active.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-dropdowneditor-active.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-textarea.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-label.dx-dropdowneditor-active.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-label.dx-dropdowneditor-active.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-label.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-label.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-label.dx-textarea.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-before,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-label.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-before {
  -webkit-box-shadow: inset 0 -2px #f44336,inset 0 2px #f44336,inset -2px 0 #f44336,inset 2px 0 transparent;
  box-shadow: inset 0 -2px #f44336,inset 0 2px #f44336,inset -2px 0 #f44336,inset 2px 0 transparent;
}
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-dropdowneditor-active.dx-state-focused.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-dropdowneditor-active.dx-state-focused.dx-state-hover.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-focused.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-focused.dx-state-hover.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-textarea.dx-state-focused.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-floating-label.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-dropdowneditor-active.dx-state-focused.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-dropdowneditor-active.dx-state-focused.dx-state-hover.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-focused.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-state-focused.dx-state-hover.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-textarea.dx-state-focused.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-editor-outlined.dx-texteditor-with-label.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid.dx-rtl .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-dropdowneditor-active.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-dropdowneditor-active.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-textarea.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-floating-label.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-label.dx-dropdowneditor-active.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-label.dx-dropdowneditor-active.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-label.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-label.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-label.dx-textarea.dx-state-focused.dx-invalid .dx-texteditor-label .dx-label-after,
.dx-swatch-additional .dx-rtl .dx-editor-outlined.dx-texteditor-with-label.dx-textarea.dx-state-focused.dx-state-hover.dx-invalid .dx-texteditor-label .dx-label-after {
  -webkit-box-shadow: inset 0 -2px #f44336,inset 0 2px #f44336,inset -2px 0 transparent,inset 2px 0 #f44336;
  box-shadow: inset 0 -2px #f44336,inset 0 2px #f44336,inset -2px 0 transparent,inset 2px 0 #f44336;
}
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-floating-label .dx-texteditor-label,
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-label .dx-texteditor-label {
  -webkit-transform: translate(0,0);
  transform: translate(0,0);
  top: 0;
  margin-top: 0;
  height: 11px;
  line-height: 11px;
}
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-floating-label .dx-placeholder::before,
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-label .dx-placeholder::before {
  padding-top: 10px;
}
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-floating-label .dx-texteditor-input,
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-floating-label.dx-lookup .dx-lookup-field,
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-label .dx-texteditor-input,
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-label.dx-lookup .dx-lookup-field {
  padding-top: 10px;
  padding-bottom: 5px;
}
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-floating-label.dx-textarea .dx-texteditor-container,
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-label.dx-textarea .dx-texteditor-container {
  padding-top: 10px;
}
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-floating-label.dx-textarea .dx-placeholder::before,
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-floating-label.dx-textarea .dx-texteditor-input,
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-label.dx-textarea .dx-placeholder::before,
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-label.dx-textarea .dx-texteditor-input {
  padding-top: 0;
}
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-floating-label.dx-textarea .dx-texteditor-label,
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-label.dx-textarea .dx-texteditor-label {
  top: 0;
}
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-floating-label.dx-state-focused .dx-placeholder,
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-floating-label.dx-textarea.dx-state-focused .dx-placeholder,
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-label.dx-state-focused .dx-placeholder,
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-label.dx-textarea.dx-state-focused .dx-placeholder {
  display: block;
}
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-floating-label.dx-state-focused .dx-lookup-field,
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-floating-label.dx-textarea.dx-state-focused .dx-lookup-field,
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-label.dx-state-focused .dx-lookup-field,
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-label.dx-textarea.dx-state-focused .dx-lookup-field {
  font-size: 13px;
}
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-floating-label.dx-state-focused .dx-texteditor-label,
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-floating-label.dx-textarea.dx-state-focused .dx-texteditor-label,
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-label.dx-state-focused .dx-texteditor-label,
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-label.dx-textarea.dx-state-focused .dx-texteditor-label {
  -webkit-transform: translate(0,0);
  transform: translate(0,0);
  font-size: 11px;
  height: 11px;
  line-height: 11px;
  top: 0;
  margin-top: 0;
}
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-floating-label.dx-state-focused.dx-textarea .dx-texteditor-label,
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-floating-label.dx-textarea.dx-state-focused.dx-textarea .dx-texteditor-label,
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-label.dx-state-focused.dx-textarea .dx-texteditor-label,
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-label.dx-textarea.dx-state-focused.dx-textarea .dx-texteditor-label {
  top: 0;
}
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-floating-label.dx-texteditor-with-before-buttons .dx-texteditor-buttons-container > .dx-button.dx-button-mode-text,
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-label.dx-texteditor-with-before-buttons .dx-texteditor-buttons-container > .dx-button.dx-button-mode-text {
  height: 22px;
  margin: 1px 5px 3px;
}
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-floating-label.dx-texteditor-with-before-buttons .dx-texteditor-buttons-container > .dx-button.dx-button-mode-text .dx-button-content,
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-label.dx-texteditor-with-before-buttons .dx-texteditor-buttons-container > .dx-button.dx-button-mode-text .dx-button-content {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  padding-top: 4px;
}
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-floating-label.dx-texteditor-with-before-buttons .dx-texteditor-buttons-container > .dx-button.dx-button-mode-text .dx-button-content .dx-icon,
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-label.dx-texteditor-with-before-buttons .dx-texteditor-buttons-container > .dx-button.dx-button-mode-text .dx-button-content .dx-icon {
  -ms-flex-item-align: center;
  align-self: center;
  margin-top: 1px;
}
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-floating-label.dx-texteditor-with-before-buttons .dx-texteditor-buttons-container > .dx-button.dx-button-mode-text.dx-button-has-text .dx-button-content .dx-icon,
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-label.dx-texteditor-with-before-buttons .dx-texteditor-buttons-container > .dx-button.dx-button-mode-text.dx-button-has-text .dx-button-content .dx-icon {
  margin-top: 3px;
}
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-floating-label.dx-texteditor-with-before-buttons .dx-texteditor-buttons-container > .dx-button.dx-button-mode-text:not(.dx-button-has-text),
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-label.dx-texteditor-with-before-buttons .dx-texteditor-buttons-container > .dx-button.dx-button-mode-text:not(.dx-button-has-text) {
  min-width: 22px;
}
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-floating-label.dx-texteditor-with-before-buttons .dx-texteditor-buttons-container > .dx-button.dx-button-mode-text:not(.dx-button-has-text) .dx-button-content,
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-label.dx-texteditor-with-before-buttons .dx-texteditor-buttons-container > .dx-button.dx-button-mode-text:not(.dx-button-has-text) .dx-button-content {
  padding: 2px;
}
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-floating-label.dx-texteditor-with-before-buttons .dx-texteditor-buttons-container:first-child > .dx-button:first-child,
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-label.dx-texteditor-with-before-buttons .dx-texteditor-buttons-container:first-child > .dx-button:first-child {
  margin-left: 0;
}
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-floating-label.dx-texteditor-with-before-buttons .dx-texteditor-buttons-container:last-child > .dx-button:last-child,
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-label.dx-texteditor-with-before-buttons .dx-texteditor-buttons-container:last-child > .dx-button:last-child {
  margin-right: 0;
}
.dx-swatch-additional .dx-rtl.dx-editor-underlined.dx-texteditor-with-floating-label.dx-texteditor-with-before-buttons .dx-texteditor-buttons-container:first-child > .dx-button:first-child,
.dx-swatch-additional .dx-rtl.dx-editor-underlined.dx-texteditor-with-label.dx-texteditor-with-before-buttons .dx-texteditor-buttons-container:first-child > .dx-button:first-child {
  margin-left: 5px;
  margin-right: 0;
}
.dx-swatch-additional .dx-rtl.dx-editor-underlined.dx-texteditor-with-floating-label.dx-texteditor-with-before-buttons .dx-texteditor-buttons-container:last-child > .dx-button:first-child,
.dx-swatch-additional .dx-rtl.dx-editor-underlined.dx-texteditor-with-label.dx-texteditor-with-before-buttons .dx-texteditor-buttons-container:last-child > .dx-button:first-child {
  margin-left: 5px;
  margin-right: 5px;
}
.dx-swatch-additional .dx-rtl.dx-editor-underlined.dx-texteditor-with-floating-label.dx-texteditor-with-before-buttons .dx-texteditor-buttons-container:last-child > .dx-button:last-child,
.dx-swatch-additional .dx-rtl.dx-editor-underlined.dx-texteditor-with-label.dx-texteditor-with-before-buttons .dx-texteditor-buttons-container:last-child > .dx-button:last-child {
  margin-left: 0;
  margin-right: 5px;
}
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-floating-label.dx-lookup-empty .dx-placeholder,
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly .dx-placeholder,
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-floating-label.dx-texteditor-empty .dx-placeholder,
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly .dx-placeholder {
  display: none;
}
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-floating-label.dx-lookup-empty .dx-lookup-field,
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly .dx-lookup-field,
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-floating-label.dx-texteditor-empty .dx-lookup-field,
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly .dx-lookup-field {
  font-size: 0;
}
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-floating-label.dx-lookup-empty .dx-texteditor-label,
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly .dx-texteditor-label,
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-floating-label.dx-texteditor-empty .dx-texteditor-label,
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly .dx-texteditor-label {
  -webkit-transition: font-size .2s cubic-bezier(0, 0, .2, 1),top .2s cubic-bezier(0, 0, .2, 1),-webkit-transform .2s cubic-bezier(0, 0, .2, 1);
  transition: transform .2s cubic-bezier(0, 0, .2, 1),font-size .2s cubic-bezier(0, 0, .2, 1),top .2s cubic-bezier(0, 0, .2, 1),-webkit-transform .2s cubic-bezier(0, 0, .2, 1);
  -webkit-transform: translate(0,0);
  transform: translate(0,0);
  font-size: 13px;
  top: 50%;
  height: 13px;
  margin-top: -6.5px;
  line-height: 13px;
}
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-textarea .dx-texteditor-label,
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-textarea .dx-texteditor-label,
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-textarea .dx-texteditor-label,
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-textarea .dx-texteditor-label {
  top: 10px;
  margin-top: 0;
  -webkit-transform: translate(0,0);
  transform: translate(0,0);
  font-size: 13px;
  line-height: 13px;
}
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-state-readonly.dx-textarea .dx-placeholder,
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-floating-label.dx-lookup-empty.dx-textarea .dx-placeholder,
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-state-readonly.dx-textarea .dx-placeholder,
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-floating-label.dx-texteditor-empty.dx-textarea .dx-placeholder {
  display: none;
}
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-floating-label.dx-dropdowneditor-active .dx-placeholder,
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-floating-label.dx-state-focused .dx-placeholder,
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-floating-label.dx-textarea.dx-state-focused .dx-placeholder {
  display: block;
}
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-floating-label.dx-dropdowneditor-active .dx-texteditor-label,
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-floating-label.dx-state-focused .dx-texteditor-label,
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-floating-label.dx-textarea.dx-state-focused .dx-texteditor-label {
  -webkit-transform: translate(0,0);
  transform: translate(0,0);
  font-size: 11px;
  top: 0;
  margin-top: 0;
  height: 11px;
  line-height: 11px;
}
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-floating-label.dx-dropdowneditor-active.dx-textarea .dx-texteditor-label,
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-floating-label.dx-state-focused.dx-textarea .dx-texteditor-label,
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-floating-label.dx-textarea.dx-state-focused.dx-textarea .dx-texteditor-label {
  top: 0;
}
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-before-buttons .dx-texteditor-buttons-container > .dx-button.dx-button-mode-text {
  height: 22px;
  margin: 1px 5px 3px;
}
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-before-buttons .dx-texteditor-buttons-container > .dx-button.dx-button-mode-text .dx-button-content {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  padding-top: 4px;
}
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-before-buttons .dx-texteditor-buttons-container > .dx-button.dx-button-mode-text .dx-button-content .dx-icon {
  -ms-flex-item-align: center;
  align-self: center;
  margin-top: 1px;
}
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-before-buttons .dx-texteditor-buttons-container > .dx-button.dx-button-mode-text.dx-button-has-text .dx-button-content .dx-icon {
  margin-top: 3px;
}
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-before-buttons .dx-texteditor-buttons-container > .dx-button.dx-button-mode-text:not(.dx-button-has-text) {
  min-width: 22px;
}
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-before-buttons .dx-texteditor-buttons-container > .dx-button.dx-button-mode-text:not(.dx-button-has-text) .dx-button-content {
  padding: 2px;
}
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-before-buttons .dx-texteditor-buttons-container:first-child > .dx-button:first-child {
  margin-left: 0;
}
.dx-swatch-additional .dx-editor-underlined.dx-texteditor-with-before-buttons .dx-texteditor-buttons-container:last-child > .dx-button:last-child {
  margin-right: 0;
}
.dx-swatch-additional .dx-rtl.dx-editor-underlined.dx-texteditor-with-before-buttons .dx-texteditor-buttons-container:first-child > .dx-button:first-child {
  margin-left: 5px;
  margin-right: 0;
}
.dx-swatch-additional .dx-rtl.dx-editor-underlined.dx-texteditor-with-before-buttons .dx-texteditor-buttons-container:last-child > .dx-button:first-child {
  margin-left: 5px;
  margin-right: 5px;
}
.dx-swatch-additional .dx-rtl.dx-editor-underlined.dx-texteditor-with-before-buttons .dx-texteditor-buttons-container:last-child > .dx-button:last-child {
  margin-left: 0;
  margin-right: 5px;
}
.dx-swatch-additional .dx-valid.dx-texteditor .dx-texteditor-input-container::after {
  pointer-events: none;
  position: absolute;
  top: 50%;
  margin-top: -8px;
  width: 16px;
  height: 16px;
  text-align: center;
  line-height: 17px;
  font-size: 19px;
  font-weight: 700;
  font-family: DXIcons,sans-serif;
  color: #8bc34a;
  content: "\f005";
  opacity: 0;
  -webkit-transform: scale(0);
  transform: scale(0);
  -webkit-animation: .3s cubic-bezier(1,.008,.565,1.65) .1s forwards dx-valid-badge-frames;
  animation: .3s cubic-bezier(1,.008,.565,1.65) .1s forwards dx-valid-badge-frames;
}
.dx-swatch-additional .dx-validation-pending.dx-texteditor .dx-texteditor-input-container .dx-pending-indicator {
  pointer-events: none;
  position: absolute;
  top: 50%;
  margin-top: -8px;
  width: 16px;
  height: 16px;
}
.dx-swatch-additional .dx-rtl .dx-placeholder,
.dx-swatch-additional .dx-rtl .dx-placeholder::before {
  right: 0;
  left: auto;
}
.dx-swatch-additional .dx-searchbox .dx-icon-search {
  font: 14px/1 DXIcons;
  color: gray;
}
.dx-swatch-additional .dx-searchbox .dx-icon-search::before {
  content: "\f027";
  color: rgba(255,255,255,.54);
}
.dx-swatch-additional .dx-searchbox.dx-editor-underlined .dx-icon-search {
  position: absolute;
  top: 50%;
  margin-top: -10.75px;
  width: 21.5px;
  height: 21.5px;
  background-position: 2px 2px;
  background-size: 16px 16px;
  padding: 2px 5.5px 2px 0;
  font-size: 16px;
  text-align: center;
  line-height: 16px;
}
.dx-swatch-additional .dx-rtl .dx-searchbox.dx-editor-underlined .dx-icon-search,
.dx-swatch-additional .dx-rtl.dx-searchbox.dx-editor-underlined .dx-icon-search {
  padding-right: 0;
  padding-left: 5.5px;
}
.dx-swatch-additional .dx-searchbox.dx-editor-underlined .dx-placeholder::before,
.dx-swatch-additional .dx-searchbox.dx-editor-underlined .dx-texteditor-input {
  padding-left: 21.5px;
}
.dx-swatch-additional .dx-rtl .dx-searchbox.dx-editor-underlined .dx-placeholder::before,
.dx-swatch-additional .dx-rtl .dx-searchbox.dx-editor-underlined .dx-texteditor-input,
.dx-swatch-additional .dx-rtl.dx-searchbox.dx-editor-underlined .dx-placeholder::before,
.dx-swatch-additional .dx-rtl.dx-searchbox.dx-editor-underlined .dx-texteditor-input {
  padding-left: 0;
  padding-right: 21.5px;
}
.dx-swatch-additional .dx-searchbox.dx-editor-filled .dx-icon-search,
.dx-swatch-additional .dx-searchbox.dx-editor-outlined .dx-icon-search {
  position: absolute;
  top: 50%;
  margin-top: -16.25px;
  width: 32.5px;
  height: 32.5px;
  background-position: 8px 8px;
  background-size: 16px 16px;
  padding: 8px 5.5px 8px 11px;
  font-size: 16px;
  text-align: center;
  line-height: 16px;
}
.dx-swatch-additional .dx-rtl .dx-searchbox.dx-editor-filled .dx-icon-search,
.dx-swatch-additional .dx-rtl .dx-searchbox.dx-editor-outlined .dx-icon-search,
.dx-swatch-additional .dx-rtl.dx-searchbox.dx-editor-filled .dx-icon-search,
.dx-swatch-additional .dx-rtl.dx-searchbox.dx-editor-outlined .dx-icon-search {
  padding-right: 11px;
  padding-left: 5.5px;
}
.dx-swatch-additional .dx-searchbox.dx-editor-filled .dx-placeholder::before,
.dx-swatch-additional .dx-searchbox.dx-editor-filled .dx-texteditor-input,
.dx-swatch-additional .dx-searchbox.dx-editor-outlined .dx-placeholder::before,
.dx-swatch-additional .dx-searchbox.dx-editor-outlined .dx-texteditor-input {
  padding-left: 32.5px;
}
.dx-swatch-additional .dx-rtl .dx-searchbox.dx-editor-filled .dx-placeholder::before,
.dx-swatch-additional .dx-rtl .dx-searchbox.dx-editor-filled .dx-texteditor-input,
.dx-swatch-additional .dx-rtl .dx-searchbox.dx-editor-outlined .dx-placeholder::before,
.dx-swatch-additional .dx-rtl .dx-searchbox.dx-editor-outlined .dx-texteditor-input,
.dx-swatch-additional .dx-rtl.dx-searchbox.dx-editor-filled .dx-placeholder::before,
.dx-swatch-additional .dx-rtl.dx-searchbox.dx-editor-filled .dx-texteditor-input,
.dx-swatch-additional .dx-rtl.dx-searchbox.dx-editor-outlined .dx-placeholder::before,
.dx-swatch-additional .dx-rtl.dx-searchbox.dx-editor-outlined .dx-texteditor-input {
  padding-left: 11px;
  padding-right: 32.5px;
}
.dx-swatch-additional .dx-searchbar {
  padding-bottom: 5px;
}
.dx-swatch-additional .dx-searchbar .dx-texteditor {
  margin: 0;
}
.dx-swatch-additional .dx-loadindicator {
  width: 32px;
  height: 32px;
  display: inline-block;
  overflow: hidden;
  border: none;
  background-color: transparent;
  line-height: 100%;
}
.dx-swatch-additional .dx-loadindicator-wrapper {
  width: 100%;
  height: 100%;
  font-size: 32px;
  margin: auto;
}
.dx-swatch-additional .dx-loadindicator-image {
  background-size: contain;
  -webkit-transform-origin: 50% 50%;
  transform-origin: 50% 50%;
  background-position: 50%;
  background-repeat: no-repeat;
  background-image: url("data:image/gif;base64,R0lGODlhQABAAKECADI6RTI6Rv///////yH/C05FVFNDQVBFMi4wAwEAAAAh+QQJCQABACwAAAAAQABAAAACkIyPqcvtD6OctEpgs1ag9w1m3heW0Eia6oJi63u08BygNGzfq6ybeV/6AUHCoaZotIySoSXz6HlunNIKsnqKYinUbaTrzabCjyuZoz07wGpW+w2Py+f0uv2VtrPl5ne/zVP3B5hHtxc3eBZoeAiXSLY49wjZSFipFsk36ZWJuMn5idXiwtjpN3qHqhd61wpTAAAh+QQJCQABACwAAAAAQABAAAACk4yPqcvtD6OctNqLs968+w+G4giUI2meYQmoK+t+bBt3c22nuHbvPOzL9IKWIbFiPEqSygiz6XhCG8Cps2qNYrNUkzQ7+1rDW66BrDMf0DT1Gu1GsONvMv0Mv8/1+zi77Zd3Vwc4KGYWNihXRnfIlaiIx+gGGVmp6AiWObY51ek5GZiGGUpZajpKGrnK2ur6CotQAAAh+QQJCQACACwAAAAAQABAAAACoJSPqcvtD6OctNqLs968+w+G4kiW5omm6sq27qsADyDDCd3QuI3ssc7r1W66YRBIRAYNSmZxeWgKntAoIGCVLpXUqnPY9VLDYlzRWJaR01NtFbh+n33e77kunOOz931b7zdHVyeIlqY2ePhnuIUUd+ToBunzaNNV+RKG6UKmgwUVJ8m5JtryWLoSIInK5rfA6BorO0tba3uLm6u7y9ubUAAAIfkECQkAAwAsAAAAAEAAQAAAAqKcj6nL7Q+jnLTai7PevPsPhhwAiCKJmh+aqh1buiMsb3BcY3eu0bzO+mV8wgqxSDkiI8olpOl0BKMSKHUxvWIRWW2CdOh6ueHW+GsQnwcp9bltXpfZcTmdDrbP3WN4Xt9Stxb4Z0eIY5gn+KZYKGfmyPgX2edIqbWYePmYuRbQOQhauRlKOoqoh2eKyScperWTmtZ6ippKyyiru8vb6/t7VQAAIfkECQkAAwAsAAAAAEAAQAAAAp2cj6nL7Q+jnNSBC6reCWMOTp4Xls1ImmqHZuvbuu/aznNt02MO77yK+uk+QpOvWEohQ8clR+ncQKOaKVVEvFazWoq1C+GCI9/x6WL2otMSMfv8bsviljn9dM/rc/Y9ou9nABg4uLcW+Feod4g44Ob3uBiZN3lXRlkZd2nJSJj5tqkZytYE+ZkW5DlqlmrYillKF6N6ylqLetuoK1EAACH5BAkJAAMALAAAAABAAEAAAAKLnI+pB+2+opw0vtuq3hR7wIXi54mmRj7nOqXsK33wHF/0nZT4Ptj87vvdgsIZsfgKqJC0JRPmfL4gUii1yrpiV5ntFOTNhsfksvmMTqvX7Lb7DY/L5/S6/Y7P6/d8BLjeBfg3F0hYKHcYp6WY+BYF9+i46HZEGcmGwViZRmKpg5YySRbaWObieXlSAAAh+QQJCQADACwAAAAAQABAAAACepyPqQnt30ZctFoLs3a3e7aF2UdW4vmUKnKa46pu8Exq9O29+E5B/N/jAIcHIZFoPA4nyqbzCY1Kp9Sq9YrNarfcrvcLDovH5LL5jE6r1+y2+w2Py+f0uv2Oz+vXAH4fnVQWOJZi5kNmA3WIISOFgkL1KHIlucjV8lMAACH5BAkJAAMALAAAAABAAEAAAAJ3nI+pC+0Plpy0IohztLwbDWbeKIUmRqZiZabe4w5hTG30p926le9+CfkJGY2h8YhMKpfMpvMJjUqn1Kr1is1qt9yu9wsOi8fksvmMTqvX7Lb7DY/L5/S6/Y4fO8pBPUrcAwZyU6Q0w9G3dLJY+MS4UvVoowUpVAAAIfkECQkAAwAsAAAAAEAAQAAAAn2cj6nL7Q/jALRaK7NGt/sNat4YluJImWqEru5DvnISz/bU3Xqu23wv+wFdwqGqaCwhk5sl81R5rqLSqvWKzWq33K73Cw6Lx+Sy+YxOq9fstvsNj8vn9FBKjUlf8PmzU7yH9gc2+FXoddj1IZi4VVPWYoYCYBYwGUgYWWdSAAAh+QQJCQADACwAAAAAQABAAAACkpyPqcvtD6OctEKAs93c5N+F1AeKpkNq56qkAAsjaUwPc83e+KnvYu/rAIMbEtFkPAqTymKp6VRBK8Pp5WmdYLORLffB/ILD4ga5vDijW9K1GeOOy+f0uv2Oh73ytrbdS6c2BxjoV0cohxgnmGh46DgIGQmXx7io6GaZiYlWNUmJp7nmecnZKXoq+bnHZ9P6ylUAACH5BAkJAAMALAAAAABAAEAAAAKTnI+py+0Po5y02ouz3rz7D3YAEJbHOJomSqog675o/MG0ON8b2+oZ79PYghcgsTg8ToxKCrMpSUIh0qnjab3mso8qV8HbfhFh8XhQTp3J5TU77D614+h5PE2vw+l4vt3ddzdjlucFSOjXk2dguNboiHiotsgYCTlJ+XimOWZ5qbjI+SU6iplpGopKucra6voK+1oAACH5BAkJAAMALAAAAABAAEAAAAKenI+py+0Po5y02ouz3rz7D4biSJbmiabqyrYe4GbAHF8zvNxBndzMjeMdfD2gEEEs0o6GQNJgZA6fUemgWrVin1pitrv8So1i8JVrPQOX6ek62Fav4+45XV4ev+HtPT9NxhYX+AcGg6bng8gUlSe0VXgEOVjlFMnztRhj5wYoptnCiXQZuij4qHmKSXp15/oKGys7S1tre4ubq7urUQAAIfkECQkAAwAsAAAAAEAAQAAAAqKcj6nL7Q+jnLTai7PevPsPhhwAiCJJmiGaqh1buiMsb3BcZ3Sus7zm+2GCwguxSDkiJ6jAsqJ8QqJSB6raaB2uWIaW2h18teEEl1s2t9Dp7ZrcFr9xcXmMHffh23p6vV+HABho0OfHd7WXFnS4iNZYRgTnSAbZBYaomKeZOfmHGQkayjnquUkatkNoh4p1s8pqSilbSpsqGgqru8vb6/srVAAAIfkECQkAAwAsAAAAAEAAQAAAApqcj6nL7Q+jnNSBC6reCmcOUt4Vls+ImWqHrq6Bfu/azm5tq3huevzt+/WCwhKxCDoiOallSOkUNaMbKFUyvUpJ2kq2i+WCJ+Jx2CxFk9VrdkTmtsTndBu8nijjD/r9oI/3tScYCEhndWg4h7hImKjoxhgnyUapNuIH4zhpaYbpt/O4eflZFzMYGnkq2qkVAwn2ito6Rpt5K1EAACH5BAkJAAMALAAAAABAAEAAAAKLnI+pCe2wopxUvgur3hR7DoaDh4lmRWbnOqXsa5XwrMj0bVz4Pj487vvdgsIZsQhzIGnKpVHlZDWjUijV1Li+stqVtQsOi8fksvmMTqvX7Lb7DY/L5/S6/Y7Hf91ceR8+9XbE90dYyDaI6BAAmKimI+iYBtn2UUm5RvLoYpYiqeWJKRYaSBaaqflSAAAh+QQJCQADACwAAAAAQABAAAACeZyPqQrtD5actCaIc7S8Gw1i3iiFpkOmB2hBKpm9sufOdove+pTv/tX4CVeb4bBoTCqXzKbzCY1Kp9Sq9YrNarfcrvcLDovH5LL5jE6r1+y2+w2Py+f0ut0cLPfEe/CDXOMX6BVDWLh0yBDidNL41GgiBZkoGXGyUwAAIfkECQkAAwAsAAAAAEAAQAAAAnecj6lr4A+YnLQ2iLPdHOUPduICluY4YtuJrlE7lPDsavQ9ffjOqPzvcQCHxKLxiEwql8ym8wmNSqfUqvWKzWq33K73Cw6Lx+Sy+YxOq9fstvsNj8vn9LriEbZ1Q3s+7fXDkoJXZAIooXNkuAjBxGj49OhDBclTAAAh+QQJCQADACwAAAAAQABAAAACfpyPqcvtD+MBtFqJ87K8Bw2GRneJJkZS5xql7NuQ8KzI9D10+K3vc+97AYMrDhE2PIqMymKpaXpCl4Cp9YrNarfcrvcLDovH5LL5jE6r1+y2+w2Py+d0dEXNPCfHe37e3CcWGDYIVvhlA5hI5qLXyJiiAhkp1UX5yHV5VydSAAA7");
}
.dx-swatch-additional .dx-loadindicator-icon-custom {
  position: relative;
  width: 100%;
  height: 100%;
  background-size: 100% 100%;
  -webkit-transform-origin: 50% 50%;
  transform-origin: 50% 50%;
  -webkit-animation: 1.5s linear infinite dx-loadindicator-icon-custom-rotate;
  animation: 1.5s linear infinite dx-loadindicator-icon-custom-rotate;
}
@-webkit-keyframes dx-loadindicator-icon-custom-rotate {
  from {
    -webkit-transform: rotate(0);
    transform: rotate(0);
  }
  to {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@keyframes dx-loadindicator-icon-custom-rotate {
  from {
    -webkit-transform: rotate(0);
    transform: rotate(0);
  }
  to {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
.dx-swatch-additional .dx-loadindicator-container > .dx-loadindicator {
  top: 50%;
  left: 50%;
  position: absolute;
  margin-top: -16px;
  margin-left: -16px;
}
.dx-swatch-additional .dx-loadindicator-container > .dx-loadindicator.dx-loadindicator {
  margin-top: -16px;
  margin-left: -16px;
}
.dx-swatch-additional .dx-loadindicator-content {
  position: relative;
  height: 100%;
  width: 100%;
  -webkit-animation: 1568ms linear infinite dx-content-rotation;
  animation: 1568ms linear infinite dx-content-rotation;
}
.dx-swatch-additional .dx-loadindicator-image .dx-loadindicator-content {
  -webkit-animation: none;
  animation: none;
}
.dx-swatch-additional .dx-loadindicator-icon {
  direction: ltr;
  position: absolute;
  height: 100%;
  width: 100%;
  -webkit-animation: 5332ms cubic-bezier(.4,0,.2,1) infinite both dx-icon-rotation;
  animation: 5332ms cubic-bezier(.4,0,.2,1) infinite both dx-icon-rotation;
}
.dx-swatch-additional .dx-loadindicator-segment {
  height: 100%;
  width: 100%;
  position: absolute;
}
.dx-swatch-additional .dx-loadindicator-segment-inner {
  position: relative;
  height: 100%;
  border-color: #ff5722 #ff5722 transparent;
  border-width: .12em;
  border-style: solid;
  -webkit-animation: none;
  animation: none;
  border-radius: 50%;
}
.dx-swatch-additional .dx-loadindicator-segment0,
.dx-swatch-additional .dx-loadindicator-segment2 {
  width: 50%;
  height: 100%;
  overflow: hidden;
}
.dx-swatch-additional .dx-loadindicator-segment0 .dx-loadindicator-segment-inner,
.dx-swatch-additional .dx-loadindicator-segment2 .dx-loadindicator-segment-inner {
  width: 200%;
}
.dx-swatch-additional .dx-loadindicator-segment2 {
  left: 0;
}
.dx-swatch-additional .dx-loadindicator-segment2 .dx-loadindicator-segment-inner {
  border-right-color: transparent;
  -webkit-transform: rotate(-129deg);
  transform: rotate(-129deg);
  -webkit-animation: 1333ms cubic-bezier(.4,0,.2,1) infinite both dx-left-segment-rotation;
  animation: 1333ms cubic-bezier(.4,0,.2,1) infinite both dx-left-segment-rotation;
}
.dx-swatch-additional .dx-loadindicator-segment0 {
  right: 0;
}
.dx-swatch-additional .dx-loadindicator-segment0 .dx-loadindicator-segment-inner {
  left: -100%;
  border-left-color: transparent;
  -webkit-transform: rotate(129deg);
  transform: rotate(129deg);
  -webkit-animation: 1333ms cubic-bezier(.4,0,.2,1) infinite both dx-right-segment-rotation;
  animation: 1333ms cubic-bezier(.4,0,.2,1) infinite both dx-right-segment-rotation;
}
.dx-swatch-additional .dx-loadindicator-segment1 {
  position: absolute;
  top: 0;
  left: 45%;
  width: 10%;
  height: 100%;
  overflow: hidden;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
.dx-swatch-additional .dx-loadindicator-segment1 .dx-loadindicator-segment-inner {
  width: 1000%;
  left: -450%;
}
@-webkit-keyframes dx-content-rotation {
  to {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@keyframes dx-content-rotation {
  to {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@-webkit-keyframes dx-icon-rotation {
  12.5% {
    -webkit-transform: rotate(135deg);
    transform: rotate(135deg);
  }
  25% {
    -webkit-transform: rotate(270deg);
    transform: rotate(270deg);
  }
  37.5% {
    -webkit-transform: rotate(405deg);
    transform: rotate(405deg);
  }
  50% {
    -webkit-transform: rotate(540deg);
    transform: rotate(540deg);
  }
  62.5% {
    -webkit-transform: rotate(675deg);
    transform: rotate(675deg);
  }
  75% {
    -webkit-transform: rotate(810deg);
    transform: rotate(810deg);
  }
  87.5% {
    -webkit-transform: rotate(945deg);
    transform: rotate(945deg);
  }
  to {
    -webkit-transform: rotate(1080deg);
    transform: rotate(1080deg);
  }
}
@keyframes dx-icon-rotation {
  12.5% {
    -webkit-transform: rotate(135deg);
    transform: rotate(135deg);
  }
  25% {
    -webkit-transform: rotate(270deg);
    transform: rotate(270deg);
  }
  37.5% {
    -webkit-transform: rotate(405deg);
    transform: rotate(405deg);
  }
  50% {
    -webkit-transform: rotate(540deg);
    transform: rotate(540deg);
  }
  62.5% {
    -webkit-transform: rotate(675deg);
    transform: rotate(675deg);
  }
  75% {
    -webkit-transform: rotate(810deg);
    transform: rotate(810deg);
  }
  87.5% {
    -webkit-transform: rotate(945deg);
    transform: rotate(945deg);
  }
  to {
    -webkit-transform: rotate(1080deg);
    transform: rotate(1080deg);
  }
}
@-webkit-keyframes dx-left-segment-rotation {
  from,
  to {
    -webkit-transform: rotate(130deg);
    transform: rotate(130deg);
  }
  50% {
    -webkit-transform: rotate(-5deg);
    transform: rotate(-5deg);
  }
}
@keyframes dx-left-segment-rotation {
  from,
  to {
    -webkit-transform: rotate(130deg);
    transform: rotate(130deg);
  }
  50% {
    -webkit-transform: rotate(-5deg);
    transform: rotate(-5deg);
  }
}
@-webkit-keyframes dx-right-segment-rotation {
  from,
  to {
    -webkit-transform: rotate(-130deg);
    transform: rotate(-130deg);
  }
  50% {
    -webkit-transform: rotate(5deg);
    transform: rotate(5deg);
  }
}
@keyframes dx-right-segment-rotation {
  from,
  to {
    -webkit-transform: rotate(-130deg);
    transform: rotate(-130deg);
  }
  50% {
    -webkit-transform: rotate(5deg);
    transform: rotate(5deg);
  }
}
.dx-swatch-additional .dx-treeview-loadindicator-wrapper {
  text-align: center;
}
.dx-swatch-additional .dx-treeview-node-loadindicator {
  position: absolute;
}
.dx-swatch-additional .dx-treeview {
  height: 100%;
}
.dx-swatch-additional .dx-treeview .dx-scrollable:focus,
.dx-swatch-additional .dx-treeview :focus {
  outline: 0;
}
.dx-swatch-additional .dx-treeview .dx-empty-message {
  line-height: normal;
}
.dx-swatch-additional .dx-checkbox + .dx-treeview-node-container,
.dx-swatch-additional .dx-treeview-node-container:first-child {
  margin: 0;
  display: block;
}
.dx-swatch-additional .dx-treeview-select-all-item {
  width: 100%;
}
.dx-swatch-additional .dx-treeview-node-container {
  list-style-position: inside;
  padding: 0;
  margin: 0;
  display: none;
  overflow: hidden;
}
.dx-swatch-additional .dx-treeview-node-container.dx-treeview-node-container-opened {
  display: block;
}
.dx-swatch-additional .dx-treeview-node {
  list-style-type: none;
  position: relative;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  -webkit-user-drag: none;
}
.dx-swatch-additional .dx-treeview-node a {
  text-decoration: none;
}
.dx-swatch-additional .dx-treeview-node .dx-checkbox {
  position: absolute;
  margin: 0;
}
.dx-swatch-additional .dx-treeview-item .dx-treeview-item-content span {
  vertical-align: middle;
}
.dx-swatch-additional .dx-treeview-item.dx-state-disabled {
  opacity: .5;
}
.dx-swatch-additional .dx-treeview-toggle-item-visibility.dx-state-disabled {
  cursor: default;
}
.dx-swatch-additional .dx-rtl .dx-treeview-node-container .dx-treeview-node,
.dx-swatch-additional .dx-rtl .dx-treeview-node-container .dx-treeview-node.dx-treeview-item-with-checkbox .dx-treeview-item,
.dx-swatch-additional .dx-rtl .dx-treeview-node-container:first-child>.dx-treeview-node {
  padding-left: 0;
}
.dx-swatch-additional .dx-rtl .dx-treeview-node-container .dx-treeview-node .dx-treeview-item .dx-treeview-item-content > .dx-icon {
  margin-right: 0;
}
.dx-swatch-additional .dx-rtl .dx-treeview-toggle-item-visibility {
  left: auto;
  -webkit-transform: scaleX(-1);
  transform: scaleX(-1);
  right: 0;
}
.dx-swatch-additional .dx-treeview-node-loadindicator {
  right: 0;
  top: 8px;
  left: 0;
  width: 18px;
  height: 18px;
}
.dx-swatch-additional .dx-rtl .dx-treeview-node .dx-checkbox {
  left: auto;
}
.dx-swatch-additional .dx-rtl.dx-treeview-border-visible .dx-treeview-select-all-item {
  padding-left: 0;
  padding-right: 26px;
}
.dx-swatch-additional .dx-rtl.dx-treeview-border-visible .dx-scrollable-content > .dx-treeview-node-container {
  padding-left: 1px;
  padding-right: 8px;
}
.dx-swatch-additional .dx-rtl .dx-treeview-node {
  padding-right: 16px;
}
.dx-swatch-additional .dx-rtl .dx-treeview-item .dx-treeview-item-content > .dx-icon {
  margin-left: 5px;
}
.dx-swatch-additional .dx-rtl .dx-treeview-item-with-checkbox .dx-treeview-item {
  padding-right: 31px;
}
.dx-swatch-additional .dx-rtl .dx-treeview-item-with-checkbox .dx-checkbox {
  right: 26px;
  overflow: visible;
}
.dx-swatch-additional .dx-rtl .dx-treeview-select-all-item {
  padding-left: 0;
  padding-right: 26px;
}
.dx-swatch-additional .dx-rtl .dx-treeview-select-all-item .dx-checkbox-text {
  padding-left: 0;
  padding-right: 20px;
}
.dx-swatch-additional .dx-treeview-search {
  margin-bottom: 4px;
}
.dx-swatch-additional .dx-treeview-with-search > .dx-scrollable {
  height: calc(100% - 37px);
}
.dx-swatch-additional .dx-treeview-border-visible {
  border: 1px solid #515159;
}
.dx-swatch-additional .dx-treeview-border-visible .dx-treeview-select-all-item {
  padding-left: 26px;
}
.dx-swatch-additional .dx-treeview-border-visible .dx-scrollable-content > .dx-treeview-node-container {
  padding: 1px 1px 1px 8px;
}
.dx-swatch-additional .dx-treeview-select-all-item {
  border-bottom: 1px solid #515159;
  padding: 10px 0 10px 26px;
}
.dx-swatch-additional .dx-treeview-select-all-item .dx-checkbox-text {
  padding-left: 20px;
}
.dx-swatch-additional .dx-treeview-node {
  padding-left: 16px;
}
.dx-swatch-additional .dx-state-selected > .dx-treeview-item {
  color: #fff;
}
.dx-swatch-additional .dx-treeview-item-with-checkbox .dx-treeview-item {
  color: #fff;
  padding-left: 31px;
}
.dx-swatch-additional .dx-treeview-item-with-checkbox .dx-checkbox {
  top: 10px;
  left: 26px;
}
.dx-swatch-additional .dx-treeview-item-without-checkbox.dx-state-selected > .dx-treeview-item {
  color: #fff;
  background-color: rgba(110,110,128,.5);
}
.dx-swatch-additional .dx-treeview-item-without-checkbox.dx-state-selected > .dx-treeview-item.dx-state-hover:not(.dx-state-focused) {
  background-color: rgba(110,110,128,.4);
}
.dx-swatch-additional .dx-treeview-item-without-checkbox.dx-state-focused > .dx-treeview-item {
  background-color: rgba(255,255,255,.05);
  color: #fff;
}
.dx-swatch-additional .dx-treeview-item {
  display: block;
  cursor: pointer;
  padding: 10px 8px;
  min-height: 36px;
  line-height: 16px;
}
.dx-swatch-additional .dx-treeview-item .dx-treeview-item-content > .dx-icon {
  display: inline-block;
  vertical-align: middle;
  margin-right: 5px;
  width: 18px;
  height: 18px;
  background-position: 0 0;
  background-size: 18px 18px;
  padding: 0;
  font-size: 18px;
  text-align: center;
  line-height: 18px;
}
.dx-swatch-additional .dx-treeview-item.dx-state-hover {
  background-color: rgba(255,255,255,.05);
  color: #fff;
}
.dx-swatch-additional .dx-treeview-toggle-item-visibility {
  position: absolute;
  cursor: pointer;
  font: 18px/18px DXIcons;
  text-align: center;
  color: rgba(255,255,255,.54);
  width: 16px;
  height: 36px;
  top: 0;
  left: 0;
}
.dx-swatch-additional .dx-treeview-toggle-item-visibility::before {
  content: "\f010";
  position: absolute;
  display: block;
  width: 18px;
  top: 50%;
  margin-top: -9px;
  left: 50%;
  margin-left: -9px;
}
.dx-swatch-additional .dx-treeview-toggle-item-visibility.dx-treeview-toggle-item-visibility-opened {
  font: 18px/18px DXIcons;
  text-align: center;
}
.dx-swatch-additional .dx-treeview-toggle-item-visibility.dx-treeview-toggle-item-visibility-opened::before {
  content: "\f016";
  position: absolute;
  display: block;
  width: 18px;
  top: 50%;
  margin-top: -9px;
  left: 50%;
  margin-left: -9px;
}