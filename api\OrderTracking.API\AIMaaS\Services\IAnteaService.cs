﻿using AIMaaS.Models;
using ClientPortal.Shared.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AIMaaS.Services
{
    public interface IAnteaService
    {
        //<summary>
        //    Get assests from Antea
        //</summary>
        Task<List<AnteaAsset>> GetAssetsAsync();

        //  Task<AnteaAsset> GetAssetAsync(long assetId);

        //Get client and locations
        Task<List<AssetManagementSites>> GetAllAssetManagementSitesAsync();

        Task<List<AssetAttachments>> GetAssetAttachmentsAsync(string assetid);
        Task<List<AssetComponents>> GetAssetComponentsAsync(string assetId);
        Task<List<Inspections>> GetInspectionsAsync();
        Task<List<AssetManagementSites>> GetClientLocationDataAsync();
        Task<List<AssetManagementSites>> GetClientLocationDataByIDAsync(ICollection<string>? districtid, ICollection<string>? clientid, ICollection<string>? role);
        Task<List<InspectionAttachments>> GetInspectionAttachmentsAsync(string operationid);
        Task<List<EquipmentData>> GetEquipmentData();
        Task<List<Anomalies>> GetAnomaliesAsync();
        Task<bool> UploadSubmissionFilesToGCPBucket(SubmissionFileUpload submissionFile);
        Task<List<ResponseSubmissions>> GetSubmissionsAsync(string assetIdFilter);
        Task<string> SendSubmissionMail(Submissions submissions, List<FireStoreSubmissionFileUpload> _firestoreDocuments, IEmailService emailService);
        Task<List<SystemManagementCategories>> GetSystemManagementCategories();
        Task<string> GetCostCenter(string locationId);
        Task<List<GeneralAnalysis>> GetGeneralAnalysis(string assetId);
        Task<List<CorrosionAnalysis>> GetCorrosionAnalysisAsync(string operationId);
        Task<List<ChamberData>> GetChamberDataAsync(string assetId);

    }
}