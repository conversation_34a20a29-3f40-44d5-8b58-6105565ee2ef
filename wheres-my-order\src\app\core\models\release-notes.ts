import { UserProfile } from '../../profile/models';

export class ReleaseNotes {
    id: string;
    createdBy: string;
    notes: string;
    createdAt: Date;

    constructor(options?: Partial<ReleaseNotes>) {
        if (options) {
            for (const [key, value] of Object.entries(options)) {
                // Handle date conversion from string to Date object
                if (key === 'createdAt' && typeof value === 'string') {
                    this[key] = new Date(value);
                } else {
                    this[key] = value;
                }
            }
        }

        // Ensure createdAt is always a valid Date object
        if (!this.createdAt || !(this.createdAt instanceof Date) || isNaN(this.createdAt.getTime())) {
            this.createdAt = new Date();
        }
    }

    static create(user: UserProfile, contents: string) {
        return new ReleaseNotes({
            createdBy: user.id,
            createdAt: new Date(),
            notes: contents,
        });
    }
}
