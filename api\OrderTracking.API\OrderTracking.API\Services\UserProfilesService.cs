using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using ClientPortal.Shared.Extensions;
using ClientPortal.Shared.Models;
using Microsoft.AspNetCore.Http;
using Microsoft.Azure.Cosmos;
using Microsoft.Extensions.Configuration;
using OrderTracking.API.Models;
using OrderTracking.API.Repositories;

namespace OrderTracking.API.Services
{
    public class UserProfilesService : UserProfileRepository, IUserProfilesService
    {
        private readonly IAuthHistoryService _authHistory;
        private readonly IHttpContextAccessor _httpContextAccessor;

        #region Constructors

        public UserProfilesService(IContainerFactory containerFactory, IServiceProvider serviceProvider, IConfiguration configuration) : base(containerFactory, configuration)
        {
            _authHistory = (IAuthHistoryService) serviceProvider.GetService(typeof(IAuthHistoryService));
            _httpContextAccessor = (IHttpContextAccessor) serviceProvider.GetService(typeof(IHttpContextAccessor));
        }

        #endregion

        #region Interface Implementation

        public new async Task<UserProfile> AddAsync(UserProfile userProfile)
        {
            try
            {
                var newUserProfile = await base.AddAsync(userProfile);

                await CreateChangeEventAsync(null, newUserProfile);

                return newUserProfile;
            }
            catch (CosmosException e)
            {
                if (e.StatusCode == HttpStatusCode.Conflict)
                    return null;
                throw;
            }
        }

        public new async Task<UserProfile> UpdateAsync(UserProfile userProfile)
        {
            try
            {
                var original = await base.GetAsync(userProfile.Id);
                var updated = await base.UpdateAsync(userProfile);

                if (original.IsOnlyPermittedChanges(updated)) return updated;

                await CreateChangeEventAsync(original, updated);
                

                return updated;
            }
            catch (CosmosException e)
            {
                if (e.StatusCode == HttpStatusCode.NotFound)
                    return null;
                throw;
            }
        }

        /// <summary>
        ///     Handle updating a User where the Id field itself has changed.
        ///     This requires 1) making sure the new Id doesn't exist, then
        ///     2) doing an insert, and finally 3) a delete
        /// </summary>
        /// <param name="userProfile"></param>
        /// <param name="originalId"></param>
        /// <returns></returns>
        public async Task<UserProfile> UpdateItemIncludingIdAsync(UserProfile userProfile, string originalId) =>
            await UpdateAsync(userProfile, originalId);

        public async Task<UserProfile> AddRoleToUser(string userId, string roleId)
        {
            var userProfile = await GetAsync(userId, userId);
            if (userProfile == null) return null;

            if (!userProfile.Roles.Contains(roleId))
            {
                userProfile.Roles.Add(roleId);
                await UpdateAsync(userProfile);
            }

            await CreateChangeEventAsync(userProfile, await GetAsync(userId, userId));

            return userProfile;
        }

        public async Task DeleteUsersAsync(string[] ids)
        {
            foreach (var id in ids)
            {
                var userProfile = await GetAsync(id, id);
                await CreateChangeEventAsync(userProfile, null);
                //await Container.DeleteItemAsync<UserProfile>(id, PartitionKey.None); TODO
            }
        }

        public async Task RemoveAsync(string id)
        {
            var userProfile = await GetAsync(id, id);
            await CreateChangeEventAsync(userProfile, null);
            await base.RemoveAsync(id);
        }

        public static RoleError CanRolesBeAdded(UserProfile userProfile, string[] roleIds, string[] removedRoleIds)
        {

            // this will be true if user is attempting to add AIMaaS:Demo role with any other AIMaaS role
            if (roleIds.Select(item => item.ToLower()).Contains("aimaas:demo") &&
                ((roleIds.Select(item => item.ToLower()).Contains("aimaas:admin")) ||
                roleIds.Select(item => item.ToLower()).Contains("aimaas:edit") ||
                roleIds.Select(item => item.ToLower()).Contains("aimaas:view") ||
                roleIds.Select(item => item.ToLower()).Contains("aimaas:all") ||
                roleIds.Select(item => item.ToLower()).Contains("aimaas:district") ||
                roleIds.Select(item => item.ToLower()).Contains("aimaas:client")))
            {
                return new RoleError() { Error = true, Message = "Cannot add AIMaaS:Demo alongside other AIMaaS Roles" };
            }

            foreach (var roleId in roleIds)
            {

                // If trying to add AIMaaS:Demo, check to make sure that other AIMaaS Roles are not already assigned to user
                // Rest of code in this block is building a proper error message
                if (string.Equals(roleId.ToLower(), "aimaas:demo"))
                {
                    List<string> blockingRoles = new List<string>();
                    if (userProfile.Roles.Select(role => role.ToLower()).Contains("aimaas:admin") && !removedRoleIds.Select(role => role.ToLower()).Contains("aimaas:admin"))
                    {
                        blockingRoles.Add("AIMaaS:Admin");
                    }

                    if (userProfile.Roles.Select(role => role.ToLower()).Contains("aimaas:edit") && !removedRoleIds.Select(role => role.ToLower()).Contains("aimaas:edit"))
                    {
                        blockingRoles.Add("AIMaaS:Edit");
                    }

                    if (userProfile.Roles.Select(role => role.ToLower()).Contains("aimaas:view") && !removedRoleIds.Select(role => role.ToLower()).Contains("aimaas:view"))
                    {
                        blockingRoles.Add("AIMaaS:View");
                    }
                    if (userProfile.Roles.Select(role => role.ToLower()).Contains("aimaas:all") && !removedRoleIds.Select(role => role.ToLower()).Contains("aimaas:all"))
                    {
                        blockingRoles.Add("AIMaaS:All");
                    }
                    if (userProfile.Roles.Select(role => role.ToLower()).Contains("aimaas:district") && !removedRoleIds.Select(role => role.ToLower()).Contains("aimaas:district"))
                    {
                        blockingRoles.Add("AIMaaS:District");
                    }
                    if (userProfile.Roles.Select(role => role.ToLower()).Contains("aimaas:client") && !removedRoleIds.Select(role => role.ToLower()).Contains("aimaas:client"))
                    {
                        blockingRoles.Add("AIMaaS:Client");
                    }

                    if (blockingRoles.Count > 0)
                    {
                        string message = "AIMaaS:Demo could not be added because of existing role(s): ";
                        foreach (var role in blockingRoles)
                        {
                            if (role == blockingRoles.Last())
                            {
                                message += "and " + role + " ";
                            }
                            else
                            {
                                message += role + ",";
                            }
                        }

                        return new RoleError() { Error = true, Message = message };
                    }

                }

                // If adding AIMaaS:Admin, make sure we don't already have AIMaaS:Demo, if we do return error
                if (string.Equals(roleId.ToLower(), "aimaas:admin"))
                {
                    if (userProfile.Roles.Select(item => item.ToLower()).Contains("aimaas:demo") && !removedRoleIds.Select(role => role.ToLower()).Contains("aimaas:demo"))
                    {
                        return new RoleError()
                        {
                            Error = true,
                            Message =
                                "AIMaaS:Admin role could not be added because user already has AIMaaS:Demo role"
                        };
                    }
                }

                // If adding AIMaaS:Edit, make sure we don't already have AIMaaS:Demo, if we do return error
                if (string.Equals(roleId.ToLower(), "aimaas:edit"))
                {
                    if (userProfile.Roles.Select(item => item.ToLower()).Contains("aimaas:demo") && !removedRoleIds.Select(role => role.ToLower()).Contains("aimaas:demo"))
                    {
                        return new RoleError()
                        {
                            Error = true,
                            Message =
                                "AIMaaS:Edit role could not be added because user already has AIMaaS:Demo role"
                        };
                    }
                }

                // If adding AIMaaS:View, make sure we don't already have AIMaaS:Demo, if we do return error
                if (string.Equals(roleId.ToLower(), "aimaas:view"))
                {
                    if (userProfile.Roles.Select(item => item.ToLower()).Contains("aimaas:demo") && !removedRoleIds.Select(role => role.ToLower()).Contains("aimaas:demo"))
                    {
                        return new RoleError()
                        {
                            Error = true,
                            Message =
                                "AIMaaS:View role could not be added because user already has AIMaaS:Demo role"
                        };
                    }
                }
                // If adding AIMaaS:All, make sure we don't already have AIMaaS:Demo, if we do return error
                if (string.Equals(roleId.ToLower(), "aimaas:all"))
                {
                    if (userProfile.Roles.Select(item => item.ToLower()).Contains("aimaas:demo") && !removedRoleIds.Select(role => role.ToLower()).Contains("aimaas:demo"))
                    {
                        return new RoleError()
                        {
                            Error = true,
                            Message =
                                "AIMaaS:All role could not be added because user already has AIMaaS:Demo role"
                        };
                    }
                }
                // If adding AIMaaS:District, make sure we don't already have AIMaaS:Demo, if we do return error
                if (string.Equals(roleId.ToLower(), "aimaas:district"))
                {
                    if (userProfile.Roles.Select(item => item.ToLower()).Contains("aimaas:demo") && !removedRoleIds.Select(role => role.ToLower()).Contains("aimaas:demo"))
                    {
                        return new RoleError()
                        {
                            Error = true,
                            Message =
                                "AIMaaS:District role could not be added because user already has AIMaaS:Demo role"
                        };
                    }
                }
                // If adding AIMaaS:Client, make sure we don't already have AIMaaS:Demo, if we do return error
                if (string.Equals(roleId.ToLower(), "aimaas:client"))
                {
                    if (userProfile.Roles.Select(item => item.ToLower()).Contains("aimaas:demo") && !removedRoleIds.Select(role => role.ToLower()).Contains("aimaas:demo"))
                    {
                        return new RoleError()
                        {
                            Error = true,
                            Message =
                                "AIMaaS:Client role could not be added because user already has AIMaaS:Demo role"
                        };
                    }
                }
            }

            return new RoleError() { Error = false };
        }
        #endregion

        private async Task CreateChangeEventAsync(UserProfile oldUserProfile, UserProfile newUserProfile)
        {
            if (oldUserProfile == null && newUserProfile == null)
                throw new InvalidOperationException($"{nameof(oldUserProfile)} & {nameof(newUserProfile)} cannot both be null.");
            if (_httpContextAccessor.HttpContext.User.Identity.Name != null)
            {
                var currentUser = await GetCurrentUserAsync();

                EnsureEmail(currentUser);
                if (oldUserProfile != null) EnsureEmail(oldUserProfile);
                if (newUserProfile != null) EnsureEmail(newUserProfile);

                await _authHistory.AddItemAsync(new ChangeEvent
                {
                    Id = Guid.NewGuid().ToString(),
                    Old = oldUserProfile,
                    New = newUserProfile,
                    CreatedAt = DateTime.UtcNow,
                    User = currentUser
                });
            }
        }

        private async Task<UserProfile> GetCurrentUserAsync() => await GetAsync(_httpContextAccessor.HttpContext.User.Identity.Name.ToLower());

        private void EnsureEmail(UserProfile user)
        {
            if (user.Email is null && user.Id is not null)
                user.Email = user.Id;
        }
    }
}