<app-breadcrumbs [crumbs]="crumbs"> </app-breadcrumbs>
<div class="content-block-dashboard">
    <div class="side-by-side">
        <div
             class="dx-card dx-card-w-title content-block responsive-paddings left">
            <h1>Users</h1>
            <dx-button icon="fa fa-trash"
                       hint="Delete selected user"
                       style="margin-right: .5rem; margin-bottom: .5rem;"
                       validationGroup="validation"
                       [disabled]="!this.searchList.focusedRowKey"
                       (onClick)="userDeleteClicked($event)">
            </dx-button>
            <dx-button icon="fa fa-refresh"
                       hint="Refresh user list"
                       style="margin-right: .5rem; margin-bottom: .5rem;"
                       validationGroup="validation"
                       (onClick)="refreshClicked($event)"></dx-button>
            <dx-data-grid id="searchList"
                          #searchList
                          [dataSource]="usersDataSource"
                          [showBorders]="false"
                          [showColumnHeaders]="false"
                          [focusedRowEnabled]="true"
                          [hoverStateEnabled]="true"
                          [errorRowEnabled]="false"
                          [(focusedRowKey)]="focusedRowKey"
                          (onFocusedRowChanged)="userSelected($event)"
                          height="550px">

                <dxo-search-panel [visible]="true"
                                  [width]="250"
                                  [highlightSearchText]="false"
                                  [searchVisibleColumnsOnly]="false"
                                  placeholder="Search..."></dxo-search-panel>
                <dxo-load-panel [enabled]="true"></dxo-load-panel>
                <dxo-scrolling mode="virtual"></dxo-scrolling>
                <dxo-sorting mode="none"></dxo-sorting>
                <dxo-filter-panel [visible]="true"></dxo-filter-panel>

                <dxi-column dataField="name"
                            [allowSorting]="true"
                            [sortIndex]="0"
                            sortOrder="asc"></dxi-column>
                <dxi-column dataField="id"
                            [visible]="false">
                </dxi-column>
            </dx-data-grid>
        </div>
        <div
             class="dx-card dx-card-w-title content-block responsive-paddings right">
            <h1>User Profile</h1>
            <div class="header">
                <span *ngIf="userEdit"
                      class="name">
                    {{userEdit.givenName}} {{userEdit.surname}}</span>
            </div>
            <small class="lastLoginHeader"><span
                      *ngIf="userEdit.lastLoginDate">Last Login: &nbsp;</span>
                <span *ngIf="userEdit.lastLoginDate">
                    {{userEdit.lastLoginDate | amTimeAgo}}</span></small>
            <p *ngIf="isUserSelected && !(userEdit | workEmail)">
                <strong>
                    <i style="color: red">
                        Accounts with common personal email domains cannot be
                        given permissions. Please inform the user to create a
                        new account with their work email and retry.
                    </i>
                </strong>
            </p>
            <dx-form id="form"
                     #userProfile
                     [(formData)]="userEdit"
                     [colCount]="1"
                     validationGroup="validation"
                     [disabled]="!isUserSelected || !(userEdit | workEmail)">
                <dxi-item dataField="id"
                          [disabled]="true"
                          [editorOptions]="{ readOnly: true }"
                          [label]="{text: 'Id (Email)'}"></dxi-item>

                <dxi-item dataField="givenName"
                          [disabled]="true"
                          [editorOptions]="{ readOnly: true }"></dxi-item>

                <dxi-item dataField="surname"
                          [disabled]="true"
                          [editorOptions]="{ readOnly: true }"></dxi-item>

                <dxi-item dataField="name"
                          [isRequired]="true"></dxi-item>

                <dxi-item dataField="roles"
                          editorType="dxTagBox"
                          [isRequired]="true"
                          [editorOptions]="{
                            items: availableRolesForUser$ | async,
                            onSelectionChanged: onRolesChanged,
                            showSelectionControls: true,
                            searchEnabled: true,
                            applyValueMode: 'useButtons'}"></dxi-item>

                <dxi-item *ngIf="(userEdit | isTeamEmployee) && ((userEdit | hasRole:'aimaas:edit') || (userEdit | hasRole:'aimaas:view') || !(userEdit | hasRole:'aimaas:client')) "
                          dataField="districtIds"
                          editorType="dxTagBox"
                          [isRequired]="isDistrictRequired"
                          [editorOptions]="{
                            items: districts$ | async,
                            valueExpr: 'value',
                            displayExpr: 'label',
                            showSelectionControls: true,
                            searchEnabled: true,
                            applyValueMode: 'useButtons',
                            onValueChanged: onDistrictsChanged}">
                    <dxo-label text="Districts"></dxo-label>
                </dxi-item>
                <dxi-item *ngIf="((userEdit | hasRole:'aimaas:all') && (userEdit | isTeamEmployee)) || (userEdit | hasRole:'aimaas:client')"
                          dataField="customerAccounts"
                          editorType="dxTagBox"
                          [isRequired]="isClientRequired"
                          [editorOptions]="{
                            items: clients$ | async,
                            showSelectionControls: true,
                            searchEnabled: true,
                            applyValueMode: 'useButtons'}">
                    <dxo-label text="Clients"></dxo-label>
                </dxi-item>

                <!-- External Customer Accounts Field -->
                <!-- <dxi-item dataField="customerAccounts"
                          [template]="'customerAccountsTemplate'">
                    <dxo-label text="External Client Account Numbers">
                    </dxo-label>
                </dxi-item>
                <div *dxTemplate="let data of 'customerAccountsTemplate'">
                    <div id="customerAccounts"
                         (mouseenter)="toggleCustomerAccountTooltip()"
                         (mouseleave)="toggleCustomerAccountTooltip()">
                        <dx-tag-box [dataSource]="customerAccounts"
                                    [showSelectionControls]="false"
                                    [searchEnabled]="true"
                                    [(value)]="userEdit.customerAccounts"
                                    [searchExpr]="['externalCustomer', 'externalCustomerName']"
                                    [displayExpr]="customerDisplayExpr"
                                    [valueExpr]="'externalCustomer'"
                                    [applyValueMode]="'instantly'"
                                    placeholder="No client account numbers specified"
                                    [disabled]="!(currentProfile$ | async | hasRoles: 'WMO:DistrictManager' : 'App:Admin')">
                        </dx-tag-box>
                    </div>
                </div> -->

                <dxi-item *ngIf="(userEdit | hasRole:'aimaas:view') || (userEdit | hasRole:'aimaas:edit')"
                          dataField="assetManagementSiteIds"
                          [isRequired]="isAssetManagementSiteRequired"
                          [template]="'assetManagementSiteIdsTemplate'">
                    <dxo-label text="Asset Management Site IDs">
                    </dxo-label>
                </dxi-item>
                <!-- ====== OLD IMPLEMENTATION ======
                     ====== TODO: REMOVE ONCE REFACTORED ======
                    <div *dxTemplate="let data of 'assetManagementSiteIdsTemplate'">
                    <div id="assetManagementSiteIds">
                        <dx-tag-box [dataSource]="assetManagementSites$ | async"
                                    [showSelectionControls]="false"
                                    (onSelectionChanged)="onRolesChanged($event)"
                                    [searchEnabled]="true"
                                    [(value)]="userEdit.assetManagementSiteIds"
                                    [searchExpr]="['rsitE_GROUP', 'rsitE_NAME', 'rsitE_RID']"
                                    [displayExpr]="assetManagementSiteDisplayExpr"
                                    [valueExpr]="'rsitE_RID'"
                                    [applyValueMode]="'instantly'"
                                    placeholder="No asset management site ids specified">
                        </dx-tag-box>
                    </div>
                </div>-->
                <div *dxTemplate="let data of 'assetManagementSiteIdsTemplate'">
                    <div id="assetManagementSiteIds">
                        <dx-tag-box [dataSource]="assetManagementSites$ | async"
                                    [showSelectionControls]="true"
                                    (onSelectionChanged)="onRolesChanged($event)"
                                    [searchEnabled]="true"
                                    [searchExpr]="['locationname']"
                                    [displayExpr]="'locationname'"
                                    [valueExpr]="'locationid'"
                                    [applyValueMode]="'useButtons'"
                                    [(value)]="userEdit.assetManagementSiteIds"
                                    placeholder="No asset management site ids specified">
                        </dx-tag-box>
                    </div>
                </div>
                <dxi-item *ngIf="userEdit | hasRole:'remotemonitoring:view'"
                          dataField="remoteMonitoringIds"
                          [template]="'remoteMonitoringSiteIdsTemplate'">
                    <dxo-label text="Remote Monitoring IDs">
                    </dxo-label>
                </dxi-item>
                <div
                     *dxTemplate="let data of 'remoteMonitoringSiteIdsTemplate'">
                    <div id="remoteMonitoringSiteIds">
                        <dx-tag-box [dataSource]="readings$ | async | remoteMonitoringIds"
                                    [showSelectionControls]="false"
                                    (onSelectionChanged)="onRolesChanged($event)"
                                    [searchEnabled]="true"
                                    [(value)]="userEdit.remoteMonitoringSiteIds"
                                    [applyValueMode]="'instantly'"
                                    placeholder="No ids specified">
                        </dx-tag-box>
                    </div>
                </div>

                <dxi-item *ngIf="userEdit | hasRole:'fsm:contractor'"
                          dataField="fieldServiceManagementContractorIdentifier"
                          [template]="'fieldServiceManagementContractorIdentifierTemplate'">
                    <dxo-label text="Field Services Management Contractor">
                    </dxo-label>
                </dxi-item>
                <div
                     *dxTemplate="let data of 'fieldServiceManagementContractorIdentifierTemplate'">
                    <div id="fieldServiceManagementContractorIdentifiers">
                        <dx-select-box [dataSource]=""
                                       [showSelectionControls]="false"
                                       (onSelectionChanged)="onRolesChanged($event)"
                                       [searchEnabled]="true"
                                       placeholder="No contractor identifiers specified">
                        </dx-select-box>
                    </div>
                </div>

                <dxi-item itemType="empty"></dxi-item>
            </dx-form>

            <!-- Tooltip for customerAccounts field -->
            <!-- <dx-tooltip target="#customerAccounts"
                        [visible]="!(currentProfile$ | async | hasRoles: 'WMO:DistrictManager' : 'App:Admin') && customerAccountsTooltipVisible">
                <div *dxTemplate="let data of 'content'">
                    Only users with WMO:DistrictManager role can change External
                    Client Account Numbers
                </div>
            </dx-tooltip> -->

            <dx-button text="Update"
                       type="success"
                       (onClick)="updateClicked($event)"
                       validationGroup="validation"
                       style="margin-right: .5rem;"
                       [disabled]="!isUserSelected || !(userEdit | workEmail)">
            </dx-button>
            <dx-button text="Cancel"
                       type="normal"
                       (onClick)="cancelClicked($event)"
                       validationGroup="validation"
                       [disabled]="!isUserSelected || !(userEdit | workEmail)">
            </dx-button>
        </div>
    </div>
</div>