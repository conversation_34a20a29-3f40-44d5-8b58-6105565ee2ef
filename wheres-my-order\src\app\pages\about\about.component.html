<!-- Wait for release notes to display this part -->
<div *ngIf="releaseNotes$ | async as releaseNotes">

    <!-- Loop through release notes -->
    <div *ngFor="let release of releaseNotes; let noteIndex = index;"
         class="dx-card content-block responsive-paddings">

        <!-- Display edit/delete buttons if App:Admin -->
        <div *ngIf="isAppAdmin$ | async"
             class="release-notes-buttons">
            <dx-button icon="edit"
                       (onClick)="editReleaseNotes($event, release, noteIndex)">
            </dx-button>
            <dx-button icon="remove"
                       [disabled]="deleting"
                       (onClick)="deleteReleaseNotes($event, release)">
            </dx-button>
        </div>

        <!-- Display release notes or show edit form if chosen -->
        <div
             *ngIf="editing[noteIndex]; then editingTemplate else displayTemplate">
        </div>

        <!-- Template if editing -->
        <ng-template #editingTemplate>
            <dx-html-editor [(value)]="editedReleaseNotes"
                            height="250px">
                <dxo-toolbar [multiline]="true">
                    <dxi-item name="undo"></dxi-item>
                    <dxi-item name="redo"></dxi-item>
                    <dxi-item name="separator"></dxi-item>
                    <dxi-item name="size"
                              [acceptedValues]="['8pt', '10pt', '12pt', '14pt', '18pt', '24pt', '36pt']">
                    </dxi-item>
                    <dxi-item name="font"
                              [acceptedValues]="['Arial', 'Courier New', 'Georgia', 'Impact', 'Lucida Console', 'Tahoma', 'Times New Roman', 'Verdana']">
                    </dxi-item>
                    <dxi-item name="separator"></dxi-item>
                    <dxi-item name="bold"></dxi-item>
                    <dxi-item name="italic"></dxi-item>
                    <dxi-item name="strike"></dxi-item>
                    <dxi-item name="underline"></dxi-item>
                    <dxi-item name="separator"></dxi-item>
                    <dxi-item name="alignLeft"></dxi-item>
                    <dxi-item name="alignCenter"></dxi-item>
                    <dxi-item name="alignRight"></dxi-item>
                    <dxi-item name="alignJustify"></dxi-item>
                    <dxi-item name="separator"></dxi-item>
                    <dxi-item name="orderedList"></dxi-item>
                    <dxi-item name="bulletList"></dxi-item>
                    <dxi-item name="separator"></dxi-item>
                    <dxi-item name="header"
                              [acceptedValues]="[false, 1, 2, 3, 4, 5]">
                    </dxi-item>
                    <dxi-item name="separator"></dxi-item>
                    <dxi-item name="color"></dxi-item>
                    <dxi-item name="background"></dxi-item>
                    <dxi-item name="separator"></dxi-item>
                    <dxi-item name="link"></dxi-item>
                    <dxi-item name="image"></dxi-item>
                    <dxi-item name="separator"></dxi-item>
                    <dxi-item name="clear"></dxi-item>
                    <dxi-item name="codeBlock"></dxi-item>
                    <dxi-item name="blockquote"></dxi-item>
                </dxo-toolbar>
            </dx-html-editor>
            <dx-button text="Save"
                       type="success"
                       style="float: right; margin-top: 1rem;"
                       [disabled]="!editedReleaseNotes || updating"
                       (onClick)="updateReleaseNotes($event, release, editedReleaseNotes)">
            </dx-button>
            <dx-button text="Cancel"
                       style="float: right; margin-top: 1rem; margin-right: 1rem;"
                       (onClick)="cancelEdit($event, noteIndex)"></dx-button>
        </ng-template>

        <!-- Template if not editing -->
        <ng-template #displayTemplate>
            <div class="content"
                 [innerHTML]="release.notes | safeHtml"></div>
            <span style="float: right">
                <strong
                        style="margin-right: 1em;">{{release.createdBy}}</strong>
                <small>posted {{release.createdAt | amFromUtc | amLocal |
                    amTimeAgo}}</small>
            </span>
        </ng-template>
    </div>
</div>

<!-- Show new release notes form if App:Admin -->
<div *ngIf="isAppAdmin$ | async"
     class="dx-card content-block responsive-paddings">
    <dx-html-editor [(value)]="newReleaseNotes"
                    height="250px">
        <dxo-toolbar [multiline]="true">
            <dxi-item name="undo"></dxi-item>
            <dxi-item name="redo"></dxi-item>
            <dxi-item name="separator"></dxi-item>
            <dxi-item name="size"
                      [acceptedValues]="['8pt', '10pt', '12pt', '14pt', '18pt', '24pt', '36pt']">
            </dxi-item>
            <dxi-item name="font"
                      [acceptedValues]="['Arial', 'Courier New', 'Georgia', 'Impact', 'Lucida Console', 'Tahoma', 'Times New Roman', 'Verdana']">
            </dxi-item>
            <dxi-item name="separator"></dxi-item>
            <dxi-item name="bold"></dxi-item>
            <dxi-item name="italic"></dxi-item>
            <dxi-item name="strike"></dxi-item>
            <dxi-item name="underline"></dxi-item>
            <dxi-item name="separator"></dxi-item>
            <dxi-item name="alignLeft"></dxi-item>
            <dxi-item name="alignCenter"></dxi-item>
            <dxi-item name="alignRight"></dxi-item>
            <dxi-item name="alignJustify"></dxi-item>
            <dxi-item name="separator"></dxi-item>
            <dxi-item name="orderedList"></dxi-item>
            <dxi-item name="bulletList"></dxi-item>
            <dxi-item name="separator"></dxi-item>
            <dxi-item name="header"
                      [acceptedValues]="[false, 1, 2, 3, 4, 5]"></dxi-item>
            <dxi-item name="separator"></dxi-item>
            <dxi-item name="color"></dxi-item>
            <dxi-item name="background"></dxi-item>
            <dxi-item name="separator"></dxi-item>
            <dxi-item name="link"></dxi-item>
            <dxi-item name="image"></dxi-item>
            <dxi-item name="separator"></dxi-item>
            <dxi-item name="clear"></dxi-item>
            <dxi-item name="codeBlock"></dxi-item>
            <dxi-item name="blockquote"></dxi-item>
        </dxo-toolbar>
    </dx-html-editor>
    <dx-button text="Save"
               type="success"
               style="float: right; margin-top: 1rem;"
               id="saveButton"
               [disabled]="!newReleaseNotes || saving"
               (onClick)="saveReleaseNotes($event, newReleaseNotes)">
    </dx-button>
</div>