::ng-deep .dx-datagrid-filter-panel-text {
    white-space: normal !important;
}
#large-indicator {
    position: absolute;
    top: 40%;
    left: 50%;
    transform: translate (-50%, -50%);
    z-index: 1000;
    pointer-events: none;
}
.listpagebuttons {
    margin-left: 5px;
}
.center-text {
    display: flex;
    justify-content: center;
    align-items: flex-start;
    height: 100vh;
    width: 100%;
    text-align: center;
    position: absolute;
    top: 0;
    left: 0;
    padding-top: 35vh;
}
.top-row {
    display: flex;
    // justify-content: space-between;
    gap: 0.5px;
    width: 100%;
}