import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import {
    getAnalytics,
    provideAnalytics,
    ScreenTrackingService,
    UserTrackingService
} from '@angular/fire/analytics';
import { initializeApp, provideFirebaseApp } from '@angular/fire/app';
import { getAuth, provideAuth } from '@angular/fire/auth';
import {
    enableIndexedDbPersistence,
    getFirestore,
    provideFirestore
} from '@angular/fire/firestore';
import { getFunctions, provideFunctions } from '@angular/fire/functions';
import { getPerformance, providePerformance } from '@angular/fire/performance';
import { getStorage, provideStorage } from '@angular/fire/storage';
import { environment } from '../../environments/environment';
import { SharedModule } from '../shared';
import { ApmRoutingModule } from './apm-routing.module';
import { ApmComponent } from './apm.component';
import {
    ActivityHoursPieChartComponent,
    ActivityTrackerComponent,
    ApmAssetDetailsComponent,
    ApmAssetDetailsFiveSeventyComponent,
    ApmAssetDetailsFiveTenComponent,
    ApmAssetDetailsSixFiftyThreeComponent,
    ApmAssetPhotosComponent,
    ApmJobDetailsComponent,
    AssetAccessComponent,
    AssetCategoriesSelectorComponent,
    AssetCreationComponent,
    AssetPpeComponent,
    AssetsDetailsComponent,
    AssetsDoughnutChartComponent,
    AssetsTabComponent,
    AttachmentsTabComponent,
    BusinessUnitSelectorComponent,
    ContactsTabComponent,
    CreationWorkflowComponent,
    DailyInspectionCountComponent,
    DataSelectionComponent,
    DetailsTabComponent,
    EquipmentByAreaAndTypeComponent,
    GeneralInfoTabComponent,
    InspectionInformationComponent,
    InspectionPhotosTabComponent,
    InspectionResultsComponent,
    InspectionStatusDoughnutChartComponent,
    InspectionsWithoutDueDatesComponent,
    LeakRepairPhotosComponent,
    LeakReportingDetailsComponent,
    LeakReportingGridComponent,
    LeakReportTabComponent,
    LocationTabComponent,
    OverviewBoxesComponent,
    ProjectDetailsComponent,
    ProjectsGridComponent,
    ReportTabButtonComponent,
    ReportTabComponent,
    TasksTabComponent,
    TaskStatusByWeekComponent,
    TimelineComponent,
    WoDetailsTabComponent,
    WorkDetailsTabComponent,
    WorkOrderCreationComponent,
    WorkOrdersGridComponent
} from './components';
import { AssignTasksComponent } from './components/assign-tasks/assign-tasks.component';
import { ManageTasksComponent } from './components/manage-tasks/manage-tasks.component';
import { TaskDetailsComponent } from './components/task-details/task-details.component';
import {
    AssetManagementComponent,
    ClientManagementComponent,
    DashboardComponent,
    LeakReportingComponent,
    ProjectsComponent,
    TasksComponent,
    WorkOrderDetailsComponent,
    WorkOrdersComponent
} from './pages';
import {
    AssetAccessPipe,
    AssetDetailsFiveSeventyPipe,
    AssetDetailsFiveTenPipe,
    AssetDetailsSixFiftyThreePipe,
    AssetNumberPipe,
    AssetPpePipe,
    AssignedProjectLocationPipe,
    ContactsPipe,
    InspectionInfoPipe,
    InspectionResultsPipe,
    LeakReportGridRowsPipe,
    LeakReportInfoPipe,
    LeakReportingPhotoModalTitlePipe,
    LeakReportPhotosPipe,
    LeakReportWorkDetailsPipe,
    PhotoSasPipe,
    ProjectAssetsPipe,
    ProjectDetailPipe,
    ProjectLocationPipe,
    TaskTypesForAssetCategoryPipe,
    UserEmailsPipe,
    WoDetailPipe,
    WorkOrderGridRowsPipe,
    WoTaskDetailsPipe
} from './pipes';
import {
    ApmService,
    ClientManagementService,
    DashboardService
} from './services';

@NgModule({
    declarations: [
        ApmComponent,
        ProjectsComponent,
        WorkOrdersComponent,
        ProjectDetailsComponent,
        DetailsTabComponent,
        LocationTabComponent,
        AssetsTabComponent,
        ApmJobDetailsComponent,
        ApmAssetDetailsComponent,
        ApmAssetPhotosComponent,
        WorkOrdersGridComponent,
        AssetsDetailsComponent,
        WoDetailsTabComponent,
        TasksTabComponent,
        DashboardComponent,
        AssetsDoughnutChartComponent,
        OverviewBoxesComponent,
        InspectionStatusDoughnutChartComponent,
        DailyInspectionCountComponent,
        EquipmentByAreaAndTypeComponent,
        ActivityHoursPieChartComponent,
        InspectionResultsComponent,
        InspectionInformationComponent,
        WorkOrderDetailsComponent,
        ReportTabComponent,
        AttachmentsTabComponent,
        InspectionPhotosTabComponent,
        WorkOrderGridRowsPipe,
        ActivityTrackerComponent,
        AssetPpeComponent,
        AssetAccessComponent,
        ProjectDetailPipe,
        WoDetailPipe,
        ProjectLocationPipe,
        AssignedProjectLocationPipe,
        WoTaskDetailsPipe,
        TaskTypesForAssetCategoryPipe,
        UserEmailsPipe,
        ApmAssetDetailsFiveSeventyComponent,
        ApmAssetDetailsFiveTenComponent,
        ApmAssetDetailsSixFiftyThreeComponent,
        AssetNumberPipe,
        ProjectAssetsPipe,
        InspectionResultsPipe,
        AssetAccessPipe,
        InspectionInfoPipe,
        AssetPpePipe,
        AssetDetailsFiveTenPipe,
        AssetDetailsSixFiftyThreePipe,
        AssetDetailsFiveSeventyPipe,
        ContactsPipe,
        ContactsTabComponent,
        DataSelectionComponent,
        InspectionsWithoutDueDatesComponent,
        TaskStatusByWeekComponent,
        ProjectsGridComponent,
        TasksComponent,
        AssetManagementComponent,
        TimelineComponent,
        ClientManagementComponent,
        PhotoSasPipe,
        LeakReportingComponent,
        GeneralInfoTabComponent,
        LeakRepairPhotosComponent,
        LeakReportTabComponent,
        LeakReportingDetailsComponent,
        LeakReportingGridComponent,
        LeakReportGridRowsPipe,
        LeakReportInfoPipe,
        LeakReportingPhotoModalTitlePipe,
        WorkDetailsTabComponent,
        LeakReportPhotosPipe,
        LeakReportWorkDetailsPipe,
        ReportTabButtonComponent,
        AssetCreationComponent,
        WorkOrderCreationComponent,
        CreationWorkflowComponent,
        BusinessUnitSelectorComponent,
        AssetCategoriesSelectorComponent,
        ManageTasksComponent,
        TaskDetailsComponent,
        AssignTasksComponent
    ],
    providers: [
        ProjectLocationPipe,
        AssetNumberPipe,
        ProjectDetailPipe,
        PhotoSasPipe,
        ScreenTrackingService,
        UserTrackingService,
        ApmService,
        DashboardService,
        ClientManagementService,
        WoTaskDetailsPipe,
        TaskTypesForAssetCategoryPipe
    ],
    imports: [
        CommonModule,
        ApmRoutingModule,
        SharedModule,
        provideFirebaseApp(() => initializeApp(environment.firebase)),
        provideAnalytics(() => getAnalytics()),
        provideAuth(() => getAuth()),
        provideFirestore(() => {
            const firestore = getFirestore();
            enableIndexedDbPersistence(firestore);
            return firestore;
        }),
        provideFunctions(() => getFunctions()),
        providePerformance(() => getPerformance()),
        provideStorage(() => getStorage())
    ]
})
export class ApmModule {}
