// The file contents for the current environment will overwrite these during build.
// The build system defaults to the dev environment which uses `environment.ts`, but if you do
// `ng build --env=prod` then `environment.prod.ts` will be used instead.
// The list of which env maps to which file can be found in `.angular-cli.json`.

export const environment = {
    firebase: {
        projectId: 'asset-performance-management',
        appId: '1:375236612863:web:17c93b95060b3103bc72ca',
        databaseURL:
            'https://asset-performance-management-default-rtdb.firebaseio.com',
        storageBucket: 'asset-performance-management.appspot.com',
        locationId: 'us-central',
        apiKey: 'AIzaSyCy7hM82CoycZD_DGo62vLCbmktsBUsAjE',
        authDomain: 'asset-performance-management.firebaseapp.com',
        messagingSenderId: '375236612863',
        measurementId: 'G-L9HJZB3N14'
    },
    production: false,
    api: {
        url: 'https://localhost:5001/api'
    },
    hubs: {
        edr: 'https://localhost:5001/edr'
    },
    msal: {
        clientID: '2e305521-1e55-42bf-a6ca-aa1ff0d7bff3',
        authority:
            'https://teamincb2c.b2clogin.com/teamincb2c.onmicrosoft.com/B2C_1_signupsignin',
        forgotPasswordAuthority:
            'https://teamincb2c.b2clogin.com/teamincb2c.onmicrosoft.com/B2C_1_forgotpassword',
        redirectUri: 'http://localhost:4200',
        postLogoutRedirectUri: 'http://localhost:4200'
    },
    appInsights: {
        instrumentationKey: '95205a62-1384-45d2-bc18-d68987f479af'
    },
    credo: {
        fileShare: {
            account: 'vmdiagdisk5raqsvzbbo5e6',
            shareName: 'credosoft'
        }
    },
    apm: {
        photoContainer:
            'https://aznascpiadevsa.blob.core.windows.net/flutter-dev/'
    }
};
