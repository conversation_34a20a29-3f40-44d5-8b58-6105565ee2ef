<!--Overall Container-->
<div class="responsive-paddings">
    <dx-popup [(visible)]="submissionPopupVisible"
              [width]="1200"
              [height]="700"
              [showTitle]="true"
              [dragEnabled]="false"
              [showCloseButton]="true">
        <dxi-toolbar-item toolbar="top"
                          [text]="submissionPopupTitle"
                          location="center"
                          locateInMenu="always"></dxi-toolbar-item>

        <ng-container *ngIf="submissionPopupVisible">

            <app-client-data-submission [initialAnomaly]="initialAnomaly"
                                        (clientSubmissionTitleValueChange)="clientSubmissionTitleValueChange($event)"
                                        (formSubmittedValueChange)="clientDataFormSubmitted($event)">
            </app-client-data-submission>
        </ng-container>
    </dx-popup>
    <!--Selection Box Container-->
    <div class="content-block">
        <div *ngIf="!isLoading"
             class="left-section">
            <div class="top-row">
                <dx-select-box id="selectBox4"
                               #districtSelectionBox
                               [(value)]="selectedCostCentres"
                               [items]="districtOptions"
                               displayExpr="name"
                               valueExpr="id"
                               placeholder="Select District"
                               [showSelectionControls]="false"
                               [searchEnabled]="true"
                               [width]="150"
                               (onValueChanged)="onDistrictChange($event)">
                    <dxo-drop-down-options
                                           container="#selectBox4"></dxo-drop-down-options>
                </dx-select-box>

                <dx-select-box id="selectBox3"
                               #oisClientSelectionBox
                               [(value)]="selectedCompany"
                               [items]="clientOptions"
                               displayExpr="name"
                               valueExpr="id"
                               placeholder="Select Company"
                               [showSelectionControls]="false"
                               [searchEnabled]="true"
                               [width]="150"
                               (onValueChanged)="onClientChange($event)">
                    <dxo-drop-down-options
                                           container="#selectBox3"></dxo-drop-down-options>
                </dx-select-box>
            </div>

            <div class="bottom-row">
                <dx-select-box id="selectBox"
                               #siteSelectionBox
                               *ngIf="availableSites"
                               [items]="availableSites"
                               displayExpr="locationname"
                               [searchEnabled]="true"
                               [(value)]="selectedSite"
                               valueExpr=""
                               itemTemplate="item"
                               [stylingMode]="'filled'"
                               [width]="150"
                               (onSelectionChanged)="changeSite($event)">
                    <dxo-drop-down-options
                                           container="#selectBox"></dxo-drop-down-options>
                    <div *dxTemplate="let data of 'item'">
                        <div style="display:inline-block">{{data | siteLabel}}
                        </div>
                    </div>
                </dx-select-box>
                <dx-select-box id="selectBox2"
                               #selectionBox
                               [items]="selectionOption"
                               placeholder="Select Dashboard"
                               [value]="selectedDashboard"
                               [stylingMode]="'filled'"
                               [showClearButton]="false"
                               (onValueChanged)="dashboardChanged($event)">
                    <dxo-drop-down-options
                                           container="#selectBox2"></dxo-drop-down-options>
                </dx-select-box>


            </div>
        </div>

        <div class="right-section"
             *ngIf="!isLoading">
            <dx-button [routerLink]="['../drilldown']"
                       class="listpagebuttons"
                       style="width: 100px;">
                Equipment List
            </dx-button>

            <dx-button [routerLink]="['../inspection-drilldown']"
                       class="listpagebuttons"
                       style="width: 100px;">
                Inspections List
            </dx-button>

            <dx-button [routerLink]="['../anomaly-drilldown']"
                       class="listpagebuttons"
                       style="width: 155px;">
                Recommendations List
            </dx-button>

            <dx-button *ngIf="canSubmit"
                       (onClick)="clientSubmitDataOnclick('frombuttonclick')"
                       class="listpagebuttons"
                       style="width: 100px;">
                Action Center
            </dx-button>
        </div>

    </div>


    <!-- Equipment By Area and Type Dashboard -->
    <div *ngIf="selectedDashboard === selectionOption[0] && !isLoading">
        <app-equipment-dashboard [assets]="assetsForSite$ | async"
                                 [inspections]="inspectionsForSite$ | async">
        </app-equipment-dashboard>
    </div>
    <!-- Inspections Dashboard -->
    <div *ngIf="selectedDashboard === selectionOption[1] && !isLoading">
        <app-inspections-dashboard [assets]="assetsForSite$ | async"
                                   [inspectionsData]="inspectionsForSite$ | async">
        </app-inspections-dashboard>


    </div>
    <!-- Recommendations Dashboard -->
    <div *ngIf="selectedDashboard === selectionOption[2] && !isLoading">

        <app-recommendations-dashboard [anamolies]="anomaliesForSite$ | async">
        </app-recommendations-dashboard>

    </div>

    <div *ngIf="isLoading"
         style="width: 100%; height: 100%;">
        <dx-load-indicator class="centered"
                           id="large-indicator"
                           height="300"
                           width="300"></dx-load-indicator>
    </div>
</div>